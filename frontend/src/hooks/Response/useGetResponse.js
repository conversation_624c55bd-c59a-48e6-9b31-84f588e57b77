import baseQueryOptions from 'constants/baseQueryOptions'
import useLazyQuery from 'hooks/useLazyQuery'
import { getResponses } from 'services/response'

const useGetResponse = (props) => {
    const [trigger, { data }] = useLazyQuery(['getResponse', props], () => getResponses(props), {
        keepPreviousData: true,
        ...baseQueryOptions
    })

    return { trigger, data }

}

export default useGetResponse