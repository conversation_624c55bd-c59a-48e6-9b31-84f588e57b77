import { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { toast } from "react-toastify";
import BalanceSnapshotColumn from "constants/columns/balanceSnapshot";
import useGetAccountBalanceSnapshot from "hooks/Account/useAccountBalanceSnapshots";
import SnapshotFilter from "constants/filters/snapshots";
import useRequest from "hooks/useRequest";
import Table from "components/Table";
import moment from "moment";
import * as _ from "lodash";
import * as S from "./styled";

const Snapshots = () => {
  const [snapshots, setSnapshots] = useState([]);
  const { profileId, accountId } = useParams();
  const { data: snapshotData, actions } = useRequest({
    search: {
      profileId,
      accountId,
    },
    limit: 5,
  });

  const { data: snapshotsData, isLoading } =
    useGetAccountBalanceSnapshot(snapshotData) || [];

  const onFilter = (newFilters) => {
    const { from_date, to_date } = newFilters;
    const filters = { ...newFilters, profileId, accountId };

    if (_.isEqual(filters, snapshotData.search)) {
      return false;
    }

    const fromDateValid = moment(from_date, "YYYY-MM-DD:HH:mm").isValid();
    const toDateValid = moment(to_date, "YYYY-MM-DD:HH:mm").isValid();

    if (fromDateValid && !toDateValid) {
      toast.error("Please Enter Valid End Time");
      return false;
    } else if (!fromDateValid && toDateValid) {
      toast.error("Please Enter Valid Start Time");
      return false;
    } else if (from_date > to_date) {
      toast.error(
        "Please choose valid dates - From date must be less than To Date"
      );
    } else {
      setSnapshots([]); // Clear previous snapshots
      actions?.onFilter(filters);
    }
  };

  useEffect(() => {
    if (snapshotsData) {
      setSnapshots(snapshotsData);
    }
  }, [snapshotsData]);

  return (
    <S.BalanceSnapshot>
      <Table
        columns={BalanceSnapshotColumn}
        data={snapshots}
        heading="Balance Snapshot"
        getRowId={(row) => row.effective_on || Math.random()}
        total={snapshots?.length}
        onFilter={onFilter}
        limit={snapshotData.limit}
        filters={SnapshotFilter}
        actions={actions}
        loading={isLoading}
        filterForms={{ grid: 2 }}
      />
    </S.BalanceSnapshot>
  );
};

export default Snapshots;
