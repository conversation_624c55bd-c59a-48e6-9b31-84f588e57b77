package com.peoplestrust.util.api.common.config;

/**
 * Error constants.
 */

public enum ErrorProperty {
  INVALID_FIELD,
  INVALID_HEADER,
  INTERNAL_ERROR,
  REQUEST_EXPIRED,
  MISSING_FIELD,
  RESOURCE_NOT_FOUND,
  INVALID_LIST_SIZE,
  MISSING_HEADER,
  SERVICE_UNAVAILABLE,
  UNEXPECTED_ERROR,
  UNAUTHORIZED,
  INVALID_STATUS,
  INVALID_STATUS_TRANSITION,
  DUPLICATE_RESOURCE,
  INVALID_INPUT,
  INVALID_JWT,
  INVALID_PARAM,
  DUPLICATE_TRANSACTION,
  D<PERSON><PERSON><PERSON>ATE_INSTRUCTION;

  private final String errorMessage;

  ErrorProperty() {
    this.errorMessage = null;
  }

  ErrorProperty(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  public String getErrorMessage() {
    return errorMessage;
  }
}
