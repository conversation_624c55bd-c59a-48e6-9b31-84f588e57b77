package com.peoplestrust.transaction.api.v1.common;

import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.transaction.api.v1.util.RailCategoryUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Options;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.domain.model.CommitLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.InitiateLedgerPrefundReserveRequest;
import com.peoplestrust.transaction.domain.model.InitiateLedgerPrefundReserveRequestTransactionsInner;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionRequest;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionRequestTransactionsInner;
import com.peoplestrust.transaction.domain.model.InternalPaymentCategory;
import com.peoplestrust.transaction.domain.model.InternalPaymentRail;
import com.peoplestrust.transaction.domain.model.LedgerTransaction;
import com.peoplestrust.transaction.domain.model.PaymentCategory;
import com.peoplestrust.transaction.domain.model.PaymentRail;
import com.peoplestrust.transaction.domain.model.TransactionFlow;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.InstructionStatus;
import com.peoplestrust.transaction.persistence.entity.MonetaryUnit;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.entity.TransactionHoldType;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import net.bytebuddy.utility.RandomString;

public class TestUtil {
  public final static String JWT_TOKEN = "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
  public final static String BAD_JWT_TOKEN = "eyJraWQiOiJxeXZnRXIzV0VFT2hONlR1N2hoakg5Q3BkcXp3WGJJY0l1VFdRSCtVRWRnPSIsImFsZyI6IlJTMjU2In0.eyJzdWIiOiJjZTlhOWQ0ZC1jMWNmLTQxNWQtOTllNS1mNzg4MzZiYzFhOTIiLCJjb2duaXRvOmdyb3VwcyI6WyJHTF9BRE1JTklTVFJBVElPTiIsIkVGVF9ET0NVTUVOVEFUSU9OIiwidXMtZWFzdC0yX29IMkI1UUlVb19QZW9wbGVzVHJ1c3RBREZTIiwiRVRSQU5TRkVSX0FETUlOSVNUUkFUT1IiXSwiaXNzIjoiaHR0cHM6XC9cL2NvZ25pdG8taWRwLnVzLWVhc3QtMi5hbWF6b25hd3MuY29tXC91cy1lYXN0LTJfb0gyQjVRSVVvIiwidmVyc2lvbiI6MiwiY2xpZW50X2lkIjoiMjAxdGxhMHZhdXE3cmk2Zmx2MGpuYWIyZ2UiLCJvcmlnaW5fanRpIjoiOGUzMmFjM2YtMTQ1OS00ZmE0LWEwZWMtZjk4NGY4YzY2MmJhIiwidG9rZW5fdXNlIjoiYWNjZXNzIiwic2NvcGUiOiJvcGVuaWQgY29tLnBlb3BsZXNncm91cC5sZWRnZXIuc3RnXC9hZG1pblVJLmFsbCBlbWFpbCIsImF1dGhfdGltZSI6MTcyMjI4NDE1MywiZXhwIjoxNzIyMjg3NzUzLCJpYXQiOjE3MjIyODQxNTMsImp0aSI6IjU1YTcyNTM5LWM0MzUtNDJjMi1hODhhLWZkMTA3MTdiNDNlZiIsI.O-vUHqyUAwObCijDZx2CGUjye7czV63cYqS1tL_t_sNbVmLS2ghapd75hwAZbDnZ8h8pN1S9JkPkx5qF3r7OticHO0UAyfLUennNN1g713cYTD_pIVqXXd7WUowyvstLhaVpNwhpqTID5vuu8f3J_yHImjUBUtYGvnR4uV7WnpWEVL2LsbhCa0iB_LeDBZEws7OYBvPwqOCaf7y58EMukOUtwizMEAslEofF-5K9RXG_Q8TKApcKixnheMqDZ7IcbKlK9XLMqn-sr147BvvaPRH1DmQbrPxFSz1z6Ihw_wAI21qDKXWX-OxGv_-gYq9NF-a-HpO0qHo_GB4NDh31EA";
  public static InstructionEntity mapToInstructionEntity(Instruction instruction) {
    InstructionEntity instructionEntity = new InstructionEntity();

    instructionEntity.setInstructionRefId(instruction.getInstructionRefId());
    instructionEntity.setStatus(instruction.getStatus());
    instructionEntity.setAccountRefId(UUID.fromString(instruction.getAccountRefId()));
    instructionEntity.setPaymentRail(instruction.getPaymentRail());
    instructionEntity.setProfileRefId(UUID.fromString(instruction.getProfileRefId()));
    return instructionEntity;
  }

  public static List<Transaction> createTransactions(String accountId, String profileId) {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 =
        Transaction.builder()
            .transactionRefId(UUID.randomUUID().toString())
            .transactionFlow(TransactionFlowType.CREDIT)
            .paymentCategory(PaymentCategoryType.DEBIT_PULL)
            .amount(new BigDecimal(100))
            .monetaryUnit(String.valueOf(MonetaryUnit.CAD))
            .acceptanceDateTime(DateUtils.offsetDateTime())
            .dueDateTime(DateUtils.offsetDateTime().plusDays(2))
            .status(TransactionStatus.PENDING)
            .build();
    t1.setAccountRefId(accountId);
    t1.setProfileRefId(profileId);

    Transaction t2 =
        Transaction.builder()
            .transactionRefId(UUID.randomUUID().toString())
            .transactionFlow(TransactionFlowType.DEBIT)
            .paymentCategory(PaymentCategoryType.CREDIT_PUSH)
            .amount(new BigDecimal(100))
            .monetaryUnit(String.valueOf(MonetaryUnit.CAD))
            .acceptanceDateTime(DateUtils.offsetDateTime())
            .status(TransactionStatus.PENDING)
            .build();
    t2.setAccountRefId(accountId);
    t2.setProfileRefId(profileId);

    Transaction t3 =
        Transaction.builder()
            .transactionRefId(UUID.randomUUID().toString())
            .transactionFlow(TransactionFlowType.CREDIT)
            .paymentCategory(PaymentCategoryType.DEBIT_PULL)
            .amount(new BigDecimal(100))
            .monetaryUnit(String.valueOf(MonetaryUnit.CAD))
            .acceptanceDateTime(DateUtils.offsetDateTime())
            .status(TransactionStatus.PENDING)
            .build();
    t3.setAccountRefId(accountId);
    t3.setProfileRefId(profileId);

    list.add(t1);
    list.add(t2);
    list.add(t3);

    return list;
  }


  public static Account createAccount(String accountRefId, String profileRefId) {
    Options options = createOptions();
    Account account = Account.builder().name("CryptoFin Peer2Peer Settlement Acct").description("For all peer to peer transfers")
        .monetaryUnit(String.valueOf(com.peoplestrust.account.persistence.entity.MonetaryUnit.CAD)).options(options).status(AccountStatus.ACTIVE)
        .createdDateTime(DateUtils.offset())
        .updatedDateTime(DateUtils.offset()).refId(accountRefId).profileId(profileRefId).build();
    return account;
  }

  public static Options createOptions() {
    Options options = Options.builder().overdraftAmount(BigDecimal.valueOf(100000)).fundHoldDays(5)
        .build();
    return options;
  }

  public static InstructionEntity createInstructionAndTransactionData(String accountRefId, String profileRefId) {
    List<Transaction> transactions = TestUtil.createTransactions(accountRefId, profileRefId);
    Instruction instruction =
        Instruction.builder()
            .instructionRefId("TEST_INST" + RandomString.make(16))
            .accountRefId(accountRefId)
            .paymentRail(PaymentRailType.EFT)
            .status(com.peoplestrust.transaction.persistence.entity.InstructionStatus.PENDING)
            .profileRefId(profileRefId)
            .transactions(transactions)
            .createdDateTime(DateUtils.offset())
            .updatedDateTime(DateUtils.offset())
            .build();
    InstructionEntity instructionEntity = TestUtil.mapToInstructionEntity(instruction);
    List<TransactionEntity> transactionEntities = TestUtil.getTransactionEntities(instruction);
    transactionEntities.forEach(t -> t.setInstruction(instructionEntity));
    instructionEntity.setTransactions(transactionEntities);
    return instructionEntity;
  }

  public static InitiateLedgerTransactionRequest buildInstruction() {
    UUID uuid = UUID.randomUUID();
    String word = "TEST_INST" + RandomString.make(7);
    List<InitiateLedgerTransactionRequestTransactionsInner> transactions = buildTransactions(uuid.toString());

    InitiateLedgerTransactionRequest instruction = new InitiateLedgerTransactionRequest();

    instruction.setInstructionRefId(String.valueOf(word));
    instruction.setPaymentRail(PaymentRail.EFT);
    instruction.setTransactions(transactions);

    return instruction;
  }

  public static InitiateLedgerPrefundReserveRequest buildReserveInstruction(String transactionRefId) {
    String word = "TEST_INST" + RandomString.make(7);
    List<InitiateLedgerPrefundReserveRequestTransactionsInner> transactions = buildReserveTransactions(transactionRefId);

    InitiateLedgerPrefundReserveRequest instruction = new InitiateLedgerPrefundReserveRequest();

    instruction.setInstructionRefId(String.valueOf(word));
    instruction.setPaymentRail(InternalPaymentRail.INTERNAL);
    instruction.setTransactions(transactions);

    return instruction;
  }

  public static List<InitiateLedgerPrefundReserveRequestTransactionsInner> buildReserveTransactions(String transactionRefId) {
    List<InitiateLedgerPrefundReserveRequestTransactionsInner> list = new ArrayList<>();

    InitiateLedgerPrefundReserveRequestTransactionsInner t1 = new InitiateLedgerPrefundReserveRequestTransactionsInner();
    t1.setTransactionRefId(transactionRefId);
    t1.setPaymentCategory(InternalPaymentCategory.PREFUND_RESERVE);
    t1.setTransactionFlow(TransactionFlow.CREDIT);
    t1.setAmount(new BigDecimal(100));
    t1.setMonetaryUnit(String.valueOf(MonetaryUnit.CAD));

    InitiateLedgerPrefundReserveRequestTransactionsInner t2 = new InitiateLedgerPrefundReserveRequestTransactionsInner();
    t2.setTransactionRefId(UUID.randomUUID().toString());
    t2.setPaymentCategory(InternalPaymentCategory.CORRECTION);
    t2.setTransactionFlow(TransactionFlow.CREDIT);
    t2.setAmount(new BigDecimal(100));
    t2.setMonetaryUnit(String.valueOf(MonetaryUnit.CAD));

    list.add(t1);
    list.add(t2);
    return list;
  }


  public static List<InitiateLedgerTransactionRequestTransactionsInner> buildTransactions(String transactionRefId) {
    List<InitiateLedgerTransactionRequestTransactionsInner> list = new ArrayList<>();

    InitiateLedgerTransactionRequestTransactionsInner t1 = new InitiateLedgerTransactionRequestTransactionsInner();
    t1.setTransactionRefId(transactionRefId);
    t1.setPaymentCategory(PaymentCategory.DEBIT_PULL);
    t1.setAmount(new BigDecimal(100));
    t1.setMonetaryUnit(String.valueOf(MonetaryUnit.CAD));
    t1.setAcceptanceDateTime(DateUtils.offsetDateTime());
    t1.setDueDateTime(DateUtils.offsetDateTime().plusDays(2));

    InitiateLedgerTransactionRequestTransactionsInner t2 = new InitiateLedgerTransactionRequestTransactionsInner();
    t2.setTransactionRefId(UUID.randomUUID().toString());
    t2.setPaymentCategory(PaymentCategory.CREDIT_PUSH);
    t2.setAmount(new BigDecimal(100));
    t2.setMonetaryUnit(String.valueOf(MonetaryUnit.CAD));
    t2.setAcceptanceDateTime(DateUtils.offsetDateTime());

    InitiateLedgerTransactionRequestTransactionsInner t3 = new InitiateLedgerTransactionRequestTransactionsInner();
    t3.setTransactionRefId(UUID.randomUUID().toString());
    t3.setPaymentCategory(PaymentCategory.DEBIT_PULL);
    t3.setAmount(new BigDecimal(100));
    t3.setMonetaryUnit(String.valueOf(MonetaryUnit.CAD));
    t3.setAcceptanceDateTime(DateUtils.offsetDateTime());

    list.add(t1);
    list.add(t2);
    list.add(t3);
    return list;
  }

  public static Instruction createInstruction(String accountId, String profileId) {

    List<Transaction> transactions = createTransactions();
    Instruction instruction = Instruction.builder().id(4321).instructionRefId("TEST_INS" + UUID.randomUUID()).accountRefId(accountId)
        .paymentRail(PaymentRailType.ETRANSFER).profileRefId(profileId)
        .status(InstructionStatus.POSTED)
        .transactions(transactions).createdDateTime(DateUtils.offset())
        .updatedDateTime(DateUtils.offset()).build();
    instruction.setTransactions(transactions);
    return instruction;
  }

  public static InstructionEntity getInstructionData(Instruction instruction) {
    InstructionEntity instructionEntity = new InstructionEntity();
    instructionEntity.setId(instruction.getId());
    instructionEntity.setInstructionRefId(instruction.getInstructionRefId());
    instructionEntity.setStatus(instruction.getStatus());
    instructionEntity.setAccountRefId(UUID.fromString(instruction.getAccountRefId()));
    instructionEntity.setPaymentRail(instruction.getPaymentRail());
    instructionEntity.setProfileRefId(UUID.fromString(instruction.getProfileRefId()));

    instructionEntity.setTransactions(getTransactionEntities(instruction));
    return instructionEntity;
  }

  public static List<InstructionEntity> getInstructionList(String accountId, String profileId) {
    List<InstructionEntity> entityList = new ArrayList<>();
    for (int i = 0; i < 5; i++) {
      Instruction instruction = createInstruction(accountId, profileId);
      InstructionEntity instructionEntity = getInstructionData(instruction);
      entityList.add(instructionEntity);
    }
    return entityList;
  }

  public static List<TransactionEntity> createTransactionEntitiesForAcct(String accountId, String profileId)
  {
    // Mocking a transaction entity
    TransactionEntity transactionEntity = new TransactionEntity();
    // Mocking an instruction Entity
    Instruction instruction = TestUtil.createInstruction(accountId, profileId);
    InstructionEntity instructionEntity = TestUtil.getInstructionData(instruction);
    instructionEntity.setPaymentRail(PaymentRailType.INTERNAL);
    transactionEntity.setInstruction(instructionEntity);

    List<TransactionEntity> transactionList = new ArrayList<>();
    transactionList.add(transactionEntity);
    return transactionList;
  }

  public static List<TransactionEntity> getTransactionEntities(Instruction instruction) {
    List<TransactionEntity> ts = new ArrayList<>();
    InstructionEntity instructionEntity = new InstructionEntity();
    instructionEntity.setId(4321);
    instructionEntity.setProfileRefId(UUID.fromString("3622732e-c22a-4bc2-8edc-28a596faa65a"));
    instructionEntity.setAccountRefId(UUID.fromString("3622732e-c22a-4bc2-8edc-28a596faa645"));
    instructionEntity.setInstructionRefId("Towels464");
    instruction.getTransactions().forEach(t -> {
      TransactionEntity te = TransactionEntity.builder().transactionRefId(t.getTransactionRefId())
          .acceptanceDateTime(t.getAcceptanceDateTime().toLocalDateTime())
          .transactionFlow(RailCategoryUtil.determineTransactionFlow(instruction.getPaymentRail(), t.getPaymentCategory()))
          .instruction(instructionEntity)
          .paymentCategory(t.getPaymentCategory())
          .transactionHold(TransactionHoldType.HOLD)
          .transactionFlow(TransactionFlowType.CREDIT)
          .accountRefId(UUID.randomUUID())
          .profileRefId(UUID.randomUUID())
          .monetaryUnit(MonetaryUnit.valueOf(t.getMonetaryUnit())).amount(t.getAmount()).build();
      ts.add(te);
    });
    return ts;
  }

  public static List<Transaction> createTransactions() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime()).dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();
    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.DEBIT)
        .paymentCategory(PaymentCategoryType.CREDIT_PUSH).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime()).build();
    Transaction t3 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime()).build();

    list.add(t1);
    list.add(t2);
    list.add(t3);
    return list;
  }

  public static CommitLedgerTransactionResponse getCommitLedgerTransactionResponse() {
    CommitLedgerTransactionResponse commitLedgerTransactionResponse = new CommitLedgerTransactionResponse();

    commitLedgerTransactionResponse.setCreatedDateTime(OffsetDateTime.now());
    commitLedgerTransactionResponse.setUpdatedDateTime(OffsetDateTime.now());
    commitLedgerTransactionResponse.setInstructionRefId(UUID.randomUUID().toString());
    commitLedgerTransactionResponse.setPaymentRail(PaymentRail.ETRANSFER);
    commitLedgerTransactionResponse.setStatus(com.peoplestrust.transaction.domain.model.InstructionStatus.POSTED);
    commitLedgerTransactionResponse.setTransactions(buildLedgerTransaction());

    return commitLedgerTransactionResponse;
  }

  public static List<LedgerTransaction> buildLedgerTransaction() {
    List<LedgerTransaction> list = new ArrayList<>();

    LedgerTransaction t1 = new LedgerTransaction();
    t1.setTransactionRefId(UUID.randomUUID().toString());
    t1.setPaymentCategory(PaymentCategory.COMPLETE_PAYMENT);
    t1.setTransactionFlow(TransactionFlow.CREDIT);
    t1.setMonetaryUnit("CAD");
    t1.setStatus(com.peoplestrust.transaction.domain.model.TransactionStatus.POSTED);
    t1.setAmount(new BigDecimal(1000));
    t1.setAcceptanceDateTime(DateUtils.offsetDateTime());
    t1.setEffectiveDateTime(DateUtils.offsetDateTime());

    LedgerTransaction t2 = new LedgerTransaction();
    t2.setTransactionRefId(UUID.randomUUID().toString());
    t2.setPaymentCategory(PaymentCategory.SEND_PAYMENT);
    t2.setTransactionFlow(TransactionFlow.DEBIT);
    t2.setMonetaryUnit("CAD");
    t2.setStatus(com.peoplestrust.transaction.domain.model.TransactionStatus.POSTED);
    t2.setAmount(new BigDecimal(100));
    t2.setAcceptanceDateTime(DateUtils.offsetDateTime());
    t2.setEffectiveDateTime(DateUtils.offsetDateTime());

    list.add(t1);
    list.add(t2);
    return list;
  }

  public static List<BalanceEntity> getBalanceEntityLst(String profileId, String accountId) {
    BalanceEntity b1 = new BalanceEntity();
    b1.setProfileRefId(UUID.fromString(profileId));
    b1.setAccountRefId(UUID.fromString(accountId));
    BigDecimal credit = BigDecimal.valueOf(200);
    BigDecimal debit = BigDecimal.valueOf(50);
    BigDecimal reserve = BigDecimal.valueOf(50);
    b1.setTotalAmountDebit(debit);
    b1.setTotalReserveAmount(reserve);
    b1.setTotalAmountCredit(credit);
    b1.setTotalAmount(credit.subtract(debit).add(reserve));
    b1.setCreatedDateTime(DateUtils.offset());
    b1.setEffectiveFromDateTime(DateUtils.startOfDay(1));
    b1.setEffectiveToDateTime(DateUtils.startOfDay(0));
    List<BalanceEntity> balance14Days = new ArrayList<>();
    balance14Days.add(b1);
    return balance14Days;
  }
}
