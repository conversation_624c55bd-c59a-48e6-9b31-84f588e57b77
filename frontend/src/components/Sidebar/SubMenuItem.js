import { useEffect, useCallback, memo } from 'react'
import { useLocation } from 'react-router-dom'
import * as S from './styled'

const MenuItem = ({ route, testId, currentPath, setOpen, open }) => {
    const location = useLocation()

    const openFunc = useCallback(() => {
        if (route.path.includes(currentPath)) {
            setOpen(true)
        }
    }, [route, currentPath])

    useEffect(() => {
        // openFunc()
    }, [])

    return (
        <S.MenuItem 
            data-testid={ testId }
        >
            <S.LinkContianer>
                <S.MenuLink to={route.to} className={`${location.pathname === route.to ? 'active' : ''}`}>
                    {route.name}
                </S.MenuLink>
            </S.LinkContianer>
        </S.MenuItem>
    )
}

export default memo(MenuItem)