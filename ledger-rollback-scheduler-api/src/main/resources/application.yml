spring:
  main:
    allow-bean-definition-overriding: true
  datasource:
    transaction-ro:
      url: **************************************************************************
      username: postgres
      password: postgres
      platform: postgres
      maximumPoolSize: 20
      connectionTimeout: 1000
  jpa:
    properties:
      hibernate:
        jdbc.lob.non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
        order_inserts: true
        order_updates: true
        generate_statistics: false

app:
  name: RollbackSchedulerAPI
scheduler:
  internal:
    rollback:
      endpoint: "http://localhost:8093/v1/internal/ledger/transaction/rollback"
  rollBack:
    transactions: 0 0,20,40 0/1 ? * *
  rollback:
    expiration:
      amount:
        minutes : 5
    instructions:
      fetch:
        limit : 250
logging:
  level:
    com:
      zaxxer:
        hikari: INFO
      peoplestrust: ${LOG_LEVEL_COM_PEOPLESTRUST}
    root: WARN  # Change this to ERROR for even fewer logs
    org.hibernate.SQL: ${LOG_LEVEL_HIBERNATE}
    APIPayloadLogger: ${LOG_LEVEL_API_PAYLOAD_LOGGER}
    PerfLogger: ${LOG_LEVEL_PERF_LOGGER}
    FlowLogger: ${LOG_LEVEL_FLOW_LOGGER}
