import { useEffect, useState } from "react";
import Form from "components/Forms/Form";
import createProfileForm from "constants/forms/createProfile";
import updateProfileForm from "constants/forms/updateProfile";
import { useModal } from "contexts/ModalProvider";
import * as S from "../styled";

const CreateProfile = (props) => {
  const [data, setData] = useState(null);
  const { show, closeModal, formik } = useModal();

  const staticText = {
    type: "statictext",
    title: "User Reference ID:",
    text: props?.data?.ref_id,
  };

  const changedCreateProfileForm =
    !props.edit && props?.data?.ref_id
      ? [...createProfileForm, staticText]
      : createProfileForm;

  useEffect(() => {
    if (props.data !== data) {
      setData(props.data);
    }
  }, [props.data]);

  const handleClose = () => {
    closeModal();
    props.onReset();
  };

  return (
    <S.Modal
      show={show}
      handleClose={handleClose}
      onCreate={formik.handleSubmit}
      title={props.edit ? "Update Profile" : "Create New Profile"}
      createTxt={props.edit ? "Update Profile" : "Create Profile"}
      testId={props.testId || "create-profile-modal"}
    >
      <Form
        formData={props.edit ? updateProfileForm : changedCreateProfileForm}
        formik={formik}
        data-testid="form"
        noButton
      />
    </S.Modal>
  );
};

export default CreateProfile;
