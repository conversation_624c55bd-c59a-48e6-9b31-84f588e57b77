package com.peoplestrust.transaction.api.v1.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException;
import com.fasterxml.jackson.databind.exc.ValueInstantiationException;
import com.peoplestrust.profile.domain.model.Error;
import com.peoplestrust.util.api.common.config.ErrorProperty;
import com.peoplestrust.util.api.common.exception.*;
import com.peoplestrust.util.api.common.util.Messages;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.exception.DataException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.transaction.UnexpectedRollbackException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.io.IOException;

@ControllerAdvice
@Slf4j
public class TransactionExceptionsHandler extends ResponseEntityExceptionHandler {

  @Override
  protected ResponseEntity<Object> handleServletRequestBindingException(ServletRequestBindingException ex, HttpHeaders headers, HttpStatusCode status,
      WebRequest request) {

    if (ex instanceof MissingRequestHeaderException) {
      MissingRequestHeaderException e = (MissingRequestHeaderException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.MISSING_HEADER.name(), e.getHeaderName()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else {
      return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
    }

  }

  @Override
  protected ResponseEntity<Object> handleMissingServletRequestParameter(
      MissingServletRequestParameterException ex, HttpHeaders headers, HttpStatusCode status,
      WebRequest request) {
    ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_PARAM.name(), ex.getParameterName()));
    return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
  }

  @Override
  protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException ex, HttpHeaders headers, HttpStatusCode status,
      WebRequest request) {
    ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), Messages.INVALID_FIELD));
    return new ResponseEntity<>(eo, HttpStatus.NOT_FOUND);
  }

  @Override
  protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status,
      WebRequest request) {
    if (ex.getCause() instanceof InvalidFormatException) {
      InvalidFormatException e = (InvalidFormatException) ex.getCause();
      String fieldName = e.getPath().get(e.getPath().size() - 1).getFieldName();
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), fieldName));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    }

    if (ex.getCause() instanceof IOException) {
      IOException e = (IOException) ex.getCause();
      if (e instanceof ValueInstantiationException && e.getMessage().contains("TicketTypeEnum")) {
        ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_INPUT.name(), Messages.INVALID_TICKET_TYPE));
        return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
      }
      if (e instanceof JsonMappingException && e.getMessage().contains("Date Time")) {
        ErrorDetails eo = new ErrorDetails((new Error((ErrorProperty.INVALID_FIELD.name()), Messages.INVALID_DATE_TIME)));
        return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
      }
      ErrorDetails eo = new ErrorDetails((new Error((ErrorProperty.INVALID_FIELD.name()), Messages.INVALID_INPUT)));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    }


    IllegalArgumentException e = (IllegalArgumentException) ex.getCause().getCause();
    ErrorDetails eo;
    if (ex.getCause() instanceof UnrecognizedPropertyException) {
      UnrecognizedPropertyException ure = (UnrecognizedPropertyException) ex.getCause();

      eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), ure.getPropertyName()));
    } else if (ex.getCause() instanceof ValueInstantiationException) {
      ValueInstantiationException vie = (ValueInstantiationException) ex.getCause();
      String fieldName = vie.getPath().get(vie.getPath().size() - 1).getFieldName();
      eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), fieldName + ": " + e.getMessage()));
    } else {
      eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), e.getMessage()));
    }
    return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<?> globalException(Exception ex, WebRequest req) throws JsonProcessingException {
    if (ex instanceof ValidationException) {
      log.error("ValidationException", ex);
      ValidationException e = (ValidationException) ex;
      ErrorDetails eo = new ErrorDetails(e.getError());
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof IllegalArgumentException) {
      log.error("IllegalArgumentException", ex);
      IllegalArgumentException e = (IllegalArgumentException) ex;
      String message = ex.getMessage();
      if (message.contains("enum")) {
        String[] ls = message.split("\\.");
        message = ls[ls.length - 2] + "." + ls[ls.length - 1];
      }
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), message));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof ResourceAccessException) {
      log.error("ResourceAccessException", ex);
      ResourceAccessException e = (ResourceAccessException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.SERVICE_UNAVAILABLE.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.SERVICE_UNAVAILABLE);
    } else if (ex instanceof DuplicateInstructionException) {
      log.error("DuplicateInstructionException", ex);
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.DUPLICATE_INSTRUCTION.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof DuplicateTransactionException) {
      log.error("DuplicateTransactionException", ex);
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.DUPLICATE_TRANSACTION.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof InvalidFieldException) {
      log.error("InvalidFieldException", ex);
      InvalidFieldException e = (InvalidFieldException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof InactiveStatusException) {
      log.error("InactiveStatusException", ex);
      InactiveStatusException e = (InactiveStatusException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_STATUS.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof InvalidStatusTransitionException) {
      log.error("InvalidStatusTransitionException", ex);
      InvalidStatusTransitionException e = (InvalidStatusTransitionException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_STATUS_TRANSITION.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof InvalidListSizeException) {
      log.error("InvalidListSizeException", ex);
      InvalidListSizeException e = (InvalidListSizeException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_LIST_SIZE.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof DataException) {
      log.error("DataException", ex);
      DataException e = (DataException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.INVALID_FIELD.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof MandatoryFieldException) {
      log.error("MandatoryFieldException", ex);
      MandatoryFieldException e = (MandatoryFieldException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.MISSING_FIELD.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof UnexpectedRollbackException) {
      log.error("UnexpectedRollbackException", ex);
      UnexpectedRollbackException e = (UnexpectedRollbackException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.DUPLICATE_RESOURCE.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof ResourceNotFoundException) {
      log.error("ResourceNotFoundException", ex);
      ResourceNotFoundException e = (ResourceNotFoundException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.RESOURCE_NOT_FOUND.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.NOT_FOUND);
    } else if (ex instanceof ResourceAccountNotFoundException) {
      log.error("ResourceAccountNotFoundException", ex);
      ResourceAccountNotFoundException e = (ResourceAccountNotFoundException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.RESOURCE_NOT_FOUND.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.NOT_FOUND);
    } else if (ex instanceof HttpClientErrorException) {
      log.error("HttpClientErrorException", ex);
      HttpClientErrorException e = (HttpClientErrorException) ex;
      ObjectMapper mapper = new ObjectMapper();
      String message = ex.getMessage();
      ErrorDetails errorDetails = mapper.readValue(message.substring(message.indexOf("{\"error\":")), ErrorDetails.class);
      int statusCode = Integer.parseInt(message.substring(0, 3));
      return new ResponseEntity<>(errorDetails, HttpStatus.valueOf(statusCode));
    } else if (ex instanceof NullPointerException) {
      NullPointerException e = (NullPointerException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.UNEXPECTED_ERROR.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.BAD_REQUEST);
    } else if (ex instanceof UnauthorizedException) {
      log.error("UnauthorizedException", ex);
      UnauthorizedException e = (UnauthorizedException) ex;
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.UNAUTHORIZED.name(), e.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.UNAUTHORIZED);
    } else {
      log.error(ErrorProperty.UNEXPECTED_ERROR.name(), ex);
      ErrorDetails eo = new ErrorDetails(new Error(ErrorProperty.UNEXPECTED_ERROR.name(), ex.getMessage()));
      return new ResponseEntity<>(eo, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}

