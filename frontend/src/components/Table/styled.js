import styled from 'styled-components'

export const TableContainer = styled.div`
    .MuiDataGrid-windowContainer {
        padding-bottom: 20px;
        max-height: 340px;

        .MuiDataGrid-renderingZone {
            max-height: 260px !important;
            overflow-y: scroll;
            overflow-x: hidden;
        }
    }

    .MuiDataGrid-root {
        border-top: 1px solid #dee2e6;
    }

    .MuiDataGrid-columnHeaders {
        border-bottom: 2px solid #212529;
    }

    .MuiDataGrid-columnHeader, .MuiDataGrid-columnHeaderWrapper .MuiDataGrid-cell {
        outline: none !important;
        border-right: 1px solid #dee2e6;

        &:first-child {
            border-left: 1px solid #dee2e6;
        }

        .MuiDataGrid-columnSeparator {
            display: none;
        }

        .MuiDataGrid-columnHeaderTitle {
            font-weight: bold;
        }

        
    }

    .MuiDataGrid-columnHeaderWrapper .MuiDataGrid-cell {
        &:last-child {
            display: none;
        }
    }

    .MuiDataGrid-row {
        cursor: pointer;
        background-color: transparent !important;

        .MuiDataGrid-cell {
            outline: none !important;
            border-right: 1px solid #dee2e6;
            background-color: transparent !important;

            &:first-child {
                border-left: 1px solid #dee2e6;
            }

            &:not([data-field]) {
                display: none;
            }

            abbr {
                display: block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            
            a {
                color: initial;
                text-decoration: underline;
            }
        }

        &:nth-of-type(odd) {
            .MuiDataGrid-cell {
                background-color: white;
            }
        }
        &:nth-of-type(even) {
            .MuiDataGrid-cell {
                background-color: rgba(0, 0, 0, 0.08);
            }
        }
    }

    .MuiDataGrid-root {
        border: none;
    }


    &.hide-total {
        .MuiTablePagination-displayedRows {
            display: none;
        }
    }
`

export const Filter = styled.div`

`

export const TableFilter = styled.div`
    width: 100%;
    background: white;
    form {
        height: 100%;
        display: grid;
        grid-template-rows: 1fr auto;
        .input-container {
            margin-bottom: 15px;
        }

        button {
            max-width: 200px;
            height: 41px;
            margin: 0px;
        }
    }

`

export const TableHeader = styled.div`
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    
    h3 {
        width: 100%;
        font-size: 18px;
        text-align: left;
    }

    .table-actions {
        display:flex;
    }
`