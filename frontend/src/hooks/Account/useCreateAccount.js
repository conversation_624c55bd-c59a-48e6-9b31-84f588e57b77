import { useMutation } from 'react-query'
import queryClient from 'services/client/queryClient'
import alertMessages from 'constants/alertMessages'
import { toast } from 'react-toastify'
import { createAccountService } from 'services/account'

const useCreateAccount = () => {
    const mutation = useMutation(createAccountService, {
        onSuccess: async () => {
            toast.success(alertMessages.accountCreation)
            await queryClient.refetchQueries(['getAccount'], { active: true })
        }
    })
    
    return mutation

}

export default useCreateAccount