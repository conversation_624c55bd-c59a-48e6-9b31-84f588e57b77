package com.peoplestrust.transaction.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.service.TransactionService;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.Metadata;
import com.peoplestrust.transaction.domain.model.Metadata.TicketTypeEnum;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.JsonUtil;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.UUID;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class UpdateTransactionMetaDataControllerTest {

  private static final String URL = "/v1/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}/metadata";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();
  private static final String instructionRefId = UUID.randomUUID().toString();
  private static final String transactionRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  @MockBean
  private TransactionService transactionService;

  private HttpHeaders headers;

  private ObjectMapper  objectMapper;

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  public void updateTransactionMetaDataTest() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("12345");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));

    // Perform the PUT request
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  @Test
  public void updateTransactionMetaDataTest_TicketIdTooLong() throws Exception {
    Metadata metadata = new Metadata();
    metadata.setTicketId("12345678901234567890123456"); // 26 characters (too long)
    metadata.setNotes("Valid note");

    // Perform the PUT request and expect a 400 Bad Request due to invalid ticketId length
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void updateTransactionMetaDataTest_NotesTooShort() throws Exception {
    Metadata metadata = new Metadata();
    metadata.setTicketId("12345");
    metadata.setNotes("abcd"); // Less than 5 characters

    // Perform the PUT request and expect a 400 Bad Request due to short notes length
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void updateTransactionMetaDataTest_NoTicketIdOrNotes() throws Exception {
    Metadata metadata = new Metadata();
    // Neither ticketId nor notes are set

    // Perform the PUT request and expect a 400 Bad Request due to missing both ticketId and notes
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @ParameterizedTest
  @CsvSource({
      "12345678901234567890123456,Sample note", // ticketId too long
      "12345,abcd", // notes too short
      "12345678901234567890123456,abcd" // both ticketId too long and notes too short
  })
  public void updateTransactionMetaDataTest_InvalidInput_ShouldReturnBadRequest(String ticketId, String notes) throws Exception {
    Metadata metadata = new Metadata();
    metadata.setTicketId(ticketId);
    metadata.setNotes(notes);

    // Perform the PUT request and expect a 400 Bad Request due to invalid input
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @ParameterizedTest
  @NullAndEmptySource
  public void updateTransactionMetaDataTest_MissingTicketIdAndNotes_ShouldReturnBadRequest(String emptyValue) throws Exception {
    Metadata metadata = new Metadata();
    metadata.setTicketId(emptyValue);
    metadata.setNotes(emptyValue);

    // Perform the PUT request and expect a 400 Bad Request due to missing both ticketId and notes
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void updateTransactionMetaDataTest_Invalid_ticket_type() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("12345");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));
    String invalidRequestJson = objectMapper.writeValueAsString(metadata);
    invalidRequestJson = invalidRequestJson.replace("\"ticket_type\":\"" + metadata.getTicketType() + "\"", "\"ticket_type\":\"abc\"");
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(invalidRequestJson)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Invalid ticket_type", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_space_for_ticket_type() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("12345");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));
    String invalidRequestJson = objectMapper.writeValueAsString(metadata);
    invalidRequestJson = invalidRequestJson.replace("\"ticket_type\":\"" + metadata.getTicketType() + "\"", "\"ticket_type\":\" \"");
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(invalidRequestJson)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Invalid ticket_type", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_Ticket_type_null() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("12345");
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Either notes or ticket_type + ticket_id must be provided", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_Empty_ticket_id() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId(" ");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Invalid ticket_id", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_Null_ticket_id() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Either notes or ticket_type + ticket_id must be provided", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_blank_ticket_id() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Invalid ticket_id", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_empty_notes() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes(" ");
    metadata.setTicketId("12345");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Invalid notes", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_valid_metadata() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setTicketId("12345");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));
    // Perform the PUT request
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());
  }

  @Test
  public void updateTransactionMetaDataTest_invalid_metadata() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Either notes or ticket_type + ticket_id must be provided", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_invalid_metadata_empty_notes() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("  ");
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Invalid notes", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_invalid_metadata_notes_length() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("abc");
    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Invalid notes", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  public void updateTransactionMetaDataTest_empty_metadata() throws Exception {
    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes(" ");
    metadata.setTicketId(" ");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));

    String invalidRequestJson = objectMapper.writeValueAsString(metadata);
    invalidRequestJson = invalidRequestJson.replace("\"ticket_type\":\"" + metadata.getTicketType() + "\"", "\"ticket_type\":\" \"");

    // Perform the PUT request
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(invalidRequestJson)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest()).andReturn();

    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("INVALID_INPUT", jsonNode.get("error").get("code").asText());
    assertEquals("Invalid ticket_type", jsonNode.get("error").get("additional_information").asText());
  }


}
