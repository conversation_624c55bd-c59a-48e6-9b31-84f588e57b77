import styled from "styled-components";
import RModal from "components/Modal";

export const Modal = styled(RModal)`
  .modal-header {
    button {
      outline: none;
      background: none;
      border: none;
      box-shadow: unset;

      span {
        font-size: 30px;
        color: #252525;
      }
    }
  }

  .custom-form .input-container {
    row-gap: 20px;
  }
`;

export const MedaDataModal = styled(Modal)`
  .input-container {
    .textarea-input-wrapper {
      grid-column-start: 1;
      grid-column-end: 3;
    }
  }
`;

export const MetaDataHeader = styled('div')`
  position: absolute;
  font-size: 18px;
  left: calc(50% + 8px);
  top: 20px;
  font-weight: 500;
`
