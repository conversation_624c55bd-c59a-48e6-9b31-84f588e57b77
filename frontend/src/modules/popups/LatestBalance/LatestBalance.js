import { useModal } from "contexts/ModalProvider";
import Details from "components/Details";
import latestBalance from "constants/details/latestBalance";
import useGetAccountBalance from "hooks/Account/useGetAccountBalance";
import * as S from "../styled";

const LatestBalance = ({ profileId, accountId, testId }) => {
  const { show, closeModal } = useModal();

  const data = useGetAccountBalance(profileId, accountId);

  return (
    <S.Modal
      show={show}
      handleClose={closeModal}
      title="Latest Balance"
      testId={testId || "latest-balance-modal"}
    >
      <Details data={data} details={latestBalance} />
    </S.Modal>
  );
};

export default LatestBalance;
