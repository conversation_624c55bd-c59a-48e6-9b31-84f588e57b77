package com.peoplestrust.schedulers.service;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.entity.MonetaryUnit;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.schedulers.config.SchedulersApiConstant;
import com.peoplestrust.transaction.persistence.entity.TransactionHoldType;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.mapper.CommonMapper;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.jpa.JpaSystemException;
import org.springframework.stereotype.Service;

/**
 * Balance snapshot service implementation
 */
@Slf4j
@Service
public class BalanceSnapshotServiceImpl implements BalanceSnapshotService {

  /**
   * Transaction persistence repository
   */
  @Autowired
  ReadTransactionRepository readTransactionRepository;

  /**
   * Account persistence repository
   */
  @Autowired
  ReadAccountRepository readAccountRepository;

  /**
   * Balance persistence repository
   */
  @Autowired
  BalanceRepository balanceRepository;

  /**
   * Balance persistence repository
   */
  @Autowired
  ReadBalanceRepository readBalanceRepository;

  /**
   * {{@inheritDoc}}
   *
   * @return
   */
  @Override
  public List<BalanceEntity> generateTransactionReBalanceSnapshot() {

    List<BalanceEntity> balanceEntities = new ArrayList<>();
    try {
      balanceEntities = generateBalanceEntities();
    } catch (JpaSystemException e) {// Database exception
      log.error(SchedulersApiConstant.DATABASE_ERROR + e.getMessage(), e);
    } catch (NullPointerException e) { // Null Pointer Exception
      log.error(SchedulersApiConstant.NULL_POINTER_ERROR + e.getMessage(), e);
    } catch (Exception e) { // general exception
      log.error(SchedulersApiConstant.GENERIC_ERROR + e.getMessage(), e);
    }
    return balanceEntities;
  }

  private List<BalanceEntity> generateBalanceEntities() {
    List<BalanceEntity> balanceEntities = new ArrayList<>();
    LocalDateTime localDateTime = LocalDateTime.now();
    // Retrieve all accounts
    List<AccountEntity> accounts = readAccountRepository.findAll();

    if (accounts.isEmpty()) {
      log.info("No accounts were found");
    }

    //Converting local date and time to ET
    ZonedDateTime zonedDateTimeET = localDateTime.atZone(ZoneId.of(APICommonUtilConstant.AMERICA_TORONTO));
    log.info("generateBalanceEntities ET = {}", zonedDateTimeET);

    //Start of the day for two weeks back date and time in UTC
    OffsetDateTime effectiveFromTwoWeeksDateTime = CommonMapper.offSetToOffSet(
        zonedDateTimeET.minusWeeks(2).withHour(0).withMinute(0).withSecond(0).withNano(0).toOffsetDateTime());
    log.info("generateBalanceEntities EFFECTIVE_FROM_TWO_WEEKS_DATE_TIME(In Offset) = {}", effectiveFromTwoWeeksDateTime);

    //Start of the day in UTC
    OffsetDateTime effectiveToDateTimeStartOfDay = CommonMapper.offSetToOffSet(
        zonedDateTimeET.minusHours(1).toOffsetDateTime());

    log.info("generateBalanceEntities EFFECTIVE_TO_DATE_TIME(In Offset) = {}", effectiveToDateTimeStartOfDay);

    accounts.forEach(a -> {
      LocalDateTime effectiveFromDateTime;
      LocalDateTime effectiveToDateTime;

      // retrieve the latest snapshot from persistence
      Optional<BalanceEntity> previousSnapshot = readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(
          a.getRefId(), a.getProfileId());

      // Initialize the effectiveToDateTimeSnapshot
      OffsetDateTime effectiveToDateTimeSnapshot = null;

      // Scenario 1 -- Found previous snapshot, we have a baseline to start from
      if (!previousSnapshot.isEmpty()) {
        // Scenario 1A -- Snapshot already exists for the FROM and TO date range specified, we have nothing more to do
        if (effectiveToDateTimeStartOfDay.toLocalDateTime().equals((previousSnapshot.get().getEffectiveToDateTime()))) {
          // simply return, avoid creating duplicate record.
          log.info("balance snapshot already generated for profile_ref_id={}, account_ref_id={}", a.getProfileId(), a.getRefId());

          return;
        } else {
          BalanceEntity balanceEntityFromSnapshot = previousSnapshot.get();

          // new balance snapshot required, and this one simply continues from where the previous left off
          effectiveFromDateTime = (balanceEntityFromSnapshot.getEffectiveToDateTime());
          log.info("profile_ref_id={}, account_ref_id={}, EFFECTIVE_FROM_DATE_TIME (in UTC) = {}", a.getProfileId(), a.getRefId(), effectiveFromDateTime);

          effectiveToDateTime = (effectiveToDateTimeStartOfDay).toLocalDateTime();
          log.info("profile_ref_id={}, account_ref_id={}, EFFECTIVE_TO_DATE_TIME (in UTC) = {}", a.getProfileId(), a.getRefId(), effectiveToDateTime);

          BigDecimal previousReserve = balanceEntityFromSnapshot.getTotalReserveAmount();
          BigDecimal previousCredit = balanceEntityFromSnapshot.getTotalAmountCredit();
          BigDecimal previousDebit = balanceEntityFromSnapshot.getTotalAmountDebit();
          BigDecimal previousTotal = balanceEntityFromSnapshot.getTotalAmount();

          //Retrieve sum of all credits from persistence within a date and time range
          Optional<BigDecimal> totalAmountB = readTransactionRepository.sumAmountByDateRangeBS(a.getRefId(), a.getProfileId(),
              effectiveFromDateTime, effectiveToDateTime);

          // Retrieve sum of all the reserve credits from persistence within a date and time range
          Optional<BigDecimal> reserveTotalB = readTransactionRepository.getReserveAmountBS(a.getRefId(), a.getProfileId(),
              effectiveFromDateTime, effectiveToDateTime);

          // Retrieve sum of all the fund hold amount from persistence within a date and time range
          Optional<BigDecimal> fundHoldAmountB = readTransactionRepository.getFundHoldAmount(a.getRefId(), a.getProfileId(),
              TransactionHoldType.HOLD, effectiveFromDateTime, effectiveToDateTime);

          BigDecimal reserveTotal = reserveTotalB.isPresent() ? reserveTotalB.get() : BigDecimal.ZERO;
          BigDecimal totalAmount = totalAmountB.isPresent() ? totalAmountB.get() : BigDecimal.ZERO;
          BigDecimal fundHoldAmount = fundHoldAmountB.isPresent() ? fundHoldAmountB.get() : BigDecimal.ZERO;
          BigDecimal credit = BigDecimal.ZERO;
          BigDecimal debit = BigDecimal.ZERO;

          BigDecimal totalReserve = (reserveTotal.add(previousReserve));

          // ensure DEBUG mode before running the below
          log.info("profile_ref_id={}, account_ref_id={}, SUM(CREDITS) - SUM(DEBITS) = TOTAL_AMOUNT | {} = {}, reserveTotal={}",
              a.getProfileId(), a.getRefId(), totalAmount.doubleValue(), totalAmount.doubleValue(), reserveTotal.doubleValue());

          // construct new balance snapshot; add balance amounts from PREVIOUS snapshot
          BalanceEntity balanceEntity = new BalanceEntity();
          balanceEntity.setMonetaryUnit(MonetaryUnit.valueOf(String.valueOf(a.getMonetaryUnit())));
          balanceEntity.setProfileRefId(a.getProfileId());
          balanceEntity.setTotalAmount(totalAmount.add(previousTotal));
          balanceEntity.setAccountRefId(a.getRefId());
          balanceEntity.setTotalAmountCredit(credit.add(previousCredit));
          balanceEntity.setTotalAmountDebit(debit.add(previousDebit));
          balanceEntity.setEffectiveFromDateTime((effectiveFromDateTime));
          balanceEntity.setEffectiveToDateTime((effectiveToDateTime));
          balanceEntity.setTotalReserveAmount(totalReserve);
          // ensure DEBUG mode before running the below

          log.info("profile_ref_id={}, account_ref_id={}, SUM(RESERVE_CREDITS) - SUM(RESERVE_DEBITS) + SUM(PREVIOUS_RESERVE_TOTAL)= TOTAL_RESERVE_AMOUNT | {} + {} = {}",
              a.getProfileId(), a.getRefId(), reserveTotal.doubleValue(), previousReserve.doubleValue(), totalReserve.doubleValue());

          // persist
          balanceEntities.add(balanceRepository.save(balanceEntity));
          log.info("balance snapshot generated for profile_ref_id={}, account_ref_id={}, balance snapshot = {}",
              a.getProfileId(), a.getRefId(), balanceEntity);
        }
      } else {
        // Scenario 2 -- No previous snapshots exist, therefore MUST generate a new one
        effectiveFromDateTime = (effectiveFromTwoWeeksDateTime).toLocalDateTime();
        log.info("profile_ref_id={}, account_ref_id={}, EFFECTIVE_FROM_DATE_TIME(in UTC) = {}", a.getProfileId(), a.getRefId(), effectiveFromDateTime);

        effectiveToDateTime = (effectiveToDateTimeStartOfDay).toLocalDateTime();
        log.info("profile_ref_id={}, account_ref_id={}, EFFECTIVE_TO_DATE_TIME(in UTC) = {}", a.getProfileId(), a.getRefId(), effectiveToDateTime);

        //Retrieve sum of all credits from persistence within a date and time range
        Optional<BigDecimal> totalAmountB = readTransactionRepository.sumAmountByDateRangeBS(a.getRefId(), a.getProfileId(),
            effectiveFromDateTime, effectiveToDateTime);

        // Retrieve sum of all the reserve credits from persistence within a date and time range
        Optional<BigDecimal> reserveTotalB = readTransactionRepository.getReserveAmountBS(a.getRefId(), a.getProfileId(),
            effectiveFromDateTime, effectiveToDateTime);

        // Retrieve sum of all the fund hold amount from persistence within a date and time range
        Optional<BigDecimal> fundHoldAmountB = readTransactionRepository.getFundHoldAmount(a.getRefId(), a.getProfileId(),
            TransactionHoldType.HOLD, effectiveFromDateTime, effectiveToDateTime);

        BigDecimal reserveTotal = reserveTotalB.isPresent() ? reserveTotalB.get() : BigDecimal.ZERO;
        BigDecimal totalAmount = totalAmountB.isPresent() ? totalAmountB.get() : BigDecimal.ZERO;
        BigDecimal fundHoldAmount = fundHoldAmountB.isPresent() ? fundHoldAmountB.get() : BigDecimal.ZERO;
        BigDecimal credit = BigDecimal.ZERO;
        BigDecimal debit = BigDecimal.ZERO;

        // ensure DEBUG mode before running the below
        log.info("profile_ref_id={}, account_ref_id={}, SUM(CREDITS) - SUM(DEBITS) = TOTAL_AMOUNT | {} = {}, reserveTotal={}",
            a.getProfileId(), a.getRefId(), totalAmount.doubleValue(), totalAmount.doubleValue(), reserveTotal.doubleValue());

        // construct new balance snapshot
        BalanceEntity balanceEntity = new BalanceEntity();
        balanceEntity.setMonetaryUnit(MonetaryUnit.valueOf(String.valueOf(a.getMonetaryUnit())));
        balanceEntity.setProfileRefId(a.getProfileId());
        balanceEntity.setTotalAmount(totalAmount);
        balanceEntity.setAccountRefId(a.getRefId());
        balanceEntity.setTotalAmountCredit(credit);
        balanceEntity.setTotalAmountDebit(debit);
        balanceEntity.setEffectiveFromDateTime(effectiveFromDateTime);
        balanceEntity.setEffectiveToDateTime(effectiveToDateTime);
        balanceEntity.setTotalReserveAmount(reserveTotal);
        // ensure DEBUG mode before running the below
        log.info("profile_ref_id={}, account_ref_id={}, SUM(RESERVE_CREDITS) - SUM(RESERVE_DEBITS) = TOTAL_RESERVE_AMOUNT | {}",
            a.getProfileId(), a.getRefId(), reserveTotal.doubleValue());
        // persist
        balanceEntities.add(balanceRepository.save(balanceEntity));
        log.info("first balance snapshot generated for profile_ref_id={}, account_ref_id={}, first balance snapshot = {}",
            a.getProfileId(), a.getRefId(), balanceEntity);
      }
    });

    return balanceEntities;
  }
}