import { Link } from 'react-router-dom'
import BalanceButton from 'modules/BalanceButton'

const columns = (profileId) => [
    {
      headerName: '#',
      field: 'id',
      flex: 1,
      maxWidth: 50,
      sortable: false,
      filterable: false,
      renderCell: (index) => index.api.getRowIndex(index.row.ref_Id) + 1
    },
    {
      headerName: 'Accounts',
      field: 'name',
      flex: 1,
      renderCell:({ row }) => <Link to={`/${profileId}/${row.ref_Id}`}>{row.name}</Link>
    },
    {
      headerName: 'Balance',
      field: 'balance',
      renderCell: ({ row }) => <BalanceButton id={row.ref_Id} />,
      flex: 1,
    },
    {
      headerName: 'Status',
      field: 'status',
      flex: 1,
    }
]

export default columns