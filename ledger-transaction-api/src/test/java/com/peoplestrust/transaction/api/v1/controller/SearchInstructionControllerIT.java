package com.peoplestrust.transaction.api.v1.controller;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.domain.model.RetrieveInstructionsResponse;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.config.ErrorProperty;
import com.peoplestrust.util.api.common.exception.ValidationException;
import com.peoplestrust.util.api.common.util.Messages;

import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class SearchInstructionControllerIT {

  private static final String URL_SEARCH_INSTRUCTION = "/v1/ledger/transaction/search/instruction";
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;


  @MockBean
  ReadInstructionRepository readInstructionRepository;

  private ObjectMapper objectMapper;
  private HttpHeaders headers;

  static {
    System.setProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    objectMapper.registerModule(new JavaTimeModule())
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
        .enable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID);

    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  void searchInstruction_validInput_success() throws Exception {
    List<InstructionEntity> entityList = TestUtil.getInstructionList(accountRefId, profileRefId);
    Slice<InstructionEntity> instructionSlice = new SliceImpl<>(entityList);
    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndPaymentRail(any(), any(), any(), any()))
        .thenReturn(instructionSlice);

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_INSTRUCTION)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.ACCOUNT_ID, accountRefId)
            .param(APICommonUtilConstant.PAYMENT_RAIL, "ETRANSFER")
            .param(APICommonUtilConstant.START_DATE, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_DATE, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    RetrieveInstructionsResponse actualResponse = objectMapper.readValue(jsonResponse, RetrieveInstructionsResponse.class);
    assertEquals(5, actualResponse.getInstructions().size());
  }

  @Test
  void searchInstruction_invalidProfileId_failure() throws Exception {
    String invalidProfileId = "invalid-profile-id";

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_INSTRUCTION)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, invalidProfileId)
            .param(APICommonUtilConstant.ACCOUNT_ID, accountRefId)
            .param(APICommonUtilConstant.START_TIME, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(ErrorProperty.INVALID_FIELD.name(), error.getError().getCode());
  }

  @Test
  void searchInstruction_invalidAccountId_failure() throws Exception {
    String invalidAccountId = "invalid-account-id";

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_INSTRUCTION)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.ACCOUNT_ID, invalidAccountId)
            .param(APICommonUtilConstant.START_TIME, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(ErrorProperty.INVALID_FIELD.name(), error.getError().getCode());
  }


  @Test
  void searchInstruction_MaxItemLessThan1_failure() throws Exception {

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_INSTRUCTION)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.ACCOUNT_ID, accountRefId)
            .param(APICommonUtilConstant.START_TIME, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "0")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(ErrorProperty.INVALID_FIELD.name(), error.getError().getCode());
  }

  @Test
  void searchInstruction_MaxItemMoreThan250_failure() throws Exception {

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_INSTRUCTION)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.ACCOUNT_ID, accountRefId)
            .param(APICommonUtilConstant.START_TIME, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "260")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(ErrorProperty.INVALID_FIELD.name(), error.getError().getCode());
  }

  @Test
  void searchInstruction_missingRequiredHeader_failure() throws Exception {
    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_INSTRUCTION)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.ACCOUNT_ID, accountRefId)
            .param(APICommonUtilConstant.START_TIME, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(ErrorProperty.MISSING_HEADER.name(), error.getError().getCode());
  }

  @Test
  void searchInstruction_startTimeAfterEndTime_failure() throws Exception {

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_INSTRUCTION)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.ACCOUNT_ID, accountRefId)
            .param(APICommonUtilConstant.START_TIME, "2023-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            .param(APICommonUtilConstant.OFFSET, "10")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    ValidationException error = objectMapper.readValue(jsonResponse, ValidationException.class);
    assertEquals(Messages.END_TIME_LESS_THAN_START_TIME, error.getError().getCode());
  }

  @Test
  void searchInstruction_noPaymentRail_success() throws Exception {
    // Given: A list of instructions (mock data)
    List<InstructionEntity> entityList = TestUtil.getInstructionList(accountRefId, profileRefId);
    Slice<InstructionEntity> instructionSlice = new SliceImpl<>(entityList);

    // Mock the repository call *without* paymentRail.
    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndCreatedDateTimeBetween(
        any(),   // UUID profileRefId
        any(),   // UUID accountRefId
        any(),   // startTime
        any(),   // endTime
        any()    // Pageable
    )).thenReturn(instructionSlice);

    MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_SEARCH_INSTRUCTION)
            .headers(headers)
            .param(APICommonUtilConstant.PROFILE_ID, profileRefId)
            .param(APICommonUtilConstant.ACCOUNT_ID, accountRefId)
            // Use the same param names the controller expects, e.g. "startTime" & "endTime"
            // (not "start_date" / "end_date" if those aren't recognized).
            .param(APICommonUtilConstant.START_TIME, "2022-01-01T00:00:00")
            .param(APICommonUtilConstant.END_TIME, "2022-12-31T23:59:59")
            // Omit payment_rail to simulate "no payment rail"
            .param(APICommonUtilConstant.OFFSET, "0")
            .param(APICommonUtilConstant.MAX_ITEMS, "25")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    // Deserialize the response
    String jsonResponse = result.getResponse().getContentAsString();
    RetrieveInstructionsResponse actualResponse =
        objectMapper.readValue(jsonResponse, RetrieveInstructionsResponse.class);

    // Assert the size matches what our mock returned
    assertEquals(5, actualResponse.getInstructions().size());
  }

}
