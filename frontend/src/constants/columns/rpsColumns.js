import generateDate from "utils/generateDate"

const columns = [
    {
      headerName: '#',
      field: 'id',
      maxWidth: 50,
      flex: 1,
      renderCell:(index) => index.api.getRowIndex(index.row.id) + 1
    },
    {
      headerName: 'Date & Time',
      field: 'bin(1s)',
      flex: 1,
      valueGetter: ({ row }) => generateDate(row['bin(1s)'], 'YYYY-MMMM-DD-HH:mm:ss')
    },
    {
        headerName: 'Total',
        field: 'all',
        flex: 1,
    },
    {
        headerName: 'Initiate transaction',
        field: 'initiate',
        flex: 1,
    },
    {
        headerName: 'Commit transaction',
        field: 'commit',
        flex: 1,
    }
]

export default columns