from metrics import increment, gauge, timing
from unittest.mock import patch


def test_increment_calls_statsd_with_tags():
    with patch("metrics.statsd") as mock_statsd:
        increment("my.counter", tags=["foo:bar"])
        mock_statsd.increment.assert_called_once()

        metric, kwargs = mock_statsd.increment.call_args
        tags = kwargs["tags"]

        # env tag should match whatever ENV was set in fixtures (staging)
        assert any(tag.startswith("env:") for tag in tags)
        assert "foo:bar" in tags


def test_gauge_and_timing():
    with patch("metrics.statsd") as mock_statsd:
        gauge("g", 42)
        timing("t", 0.5)
        assert mock_statsd.gauge.called
        assert mock_statsd.timing.called
