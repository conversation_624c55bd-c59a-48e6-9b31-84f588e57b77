import generateDate from "utils/generateDate"
import { <PERSON> } from 'react-router-dom'

const columns = ({ profileId, accountId }) => ([
    {
      headerName: 'Instruction reference ID',
      field: 'instruction_ref_id',
      flex: 1,
      sortable: false,
      renderCell: ({ row }) => <Link to={`/${profileId}/${accountId}/${row?.instruction_ref_id}`}>{row?.instruction_ref_id}</Link>
    },
    {
      headerName: 'Created Date',
      field: 'created_date_time',
      flex: 1,
      valueGetter: ({ row }) => generateDate(row?.created_date_time)
    },
    {
      headerName: 'Updated Date',
      field: 'updated_date_time',
      flex: 1,
      valueGetter: ({ row }) => row?.updated_date_time ? generateDate(row.updated_date_time) : 'N/A'
    },
    {
      headerName: 'Payment rail',
      field: 'payment_rail',
      flex: 1,
    },
    {
      headerName: 'Status',
      field: 'status',
      flex: 1,
    }
  ])
  
  export default columns