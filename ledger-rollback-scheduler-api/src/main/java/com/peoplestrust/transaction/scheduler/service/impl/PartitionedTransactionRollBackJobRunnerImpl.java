package com.peoplestrust.transaction.scheduler.service.impl;

import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.scheduler.service.PartitionedTransactionRollBackJobRunner;
import com.peoplestrust.transaction.scheduler.service.TransactionServiceAdapter;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class PartitionedTransactionRollBackJobRunnerImpl implements PartitionedTransactionRollBackJobRunner {

  @Value("${scheduler.rollback.expiration.amount.minutes}")
  private long expirationAmountInMinutes;

  @Value("${scheduler.rollback.instructions.fetch.limit}")
  private int fetchLimit;

  @Autowired
  TransactionServiceAdapter transactionServiceAdapter;

  @Autowired
  ReadInstructionRepository readInstructionRepository;

  @Override
  public void runRollBackJob() {
    while (true) {
      // If profile or account status was inactive, it was a never ending loop
      List<InstructionEntity> instructionsForRollBack = fetchExpiredTransactions();
      if (instructionsForRollBack.size() == 0) {
        log.info("No instructions found for rollback, terminating the job.");
        break;
      } else {
        log.info("Found {} instructions eligible for rollback", instructionsForRollBack.size());
        try {
          transactionServiceAdapter.rollBackTransactions(instructionsForRollBack);
        } catch (Exception e) {
          log.info("Exception occurred during rollback: {}", e.getMessage());
        }
      }
    }
  }

  List<InstructionEntity> fetchExpiredTransactions() {
    return readInstructionRepository.findExpiredInstructions(DateUtils.offset().minusMinutes(expirationAmountInMinutes), PageRequest.of(0, fetchLimit));
  }
}
