apiVersion: batch/v1
kind: CronJob
metadata:
  name: etransfer-recon-job
  namespace: pg-ledger-prod   # Changed to prod namespace
  labels:
    app: etransfer-recon-job-v1
    domain: general-ledger
spec:
  schedule: "* * 31 2 *"
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: etransfer-recon-job-v1
            domain: general-ledger
        spec:
          restartPolicy: OnFailure
          containers:
            - name: etransfer-recon-job-container
              # image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/etransfer-recon-job:20250724183015
              # args:
              #   - "--s3-bucket"
              #   - "pg-gl-recon-prod"    # Hardcoded for security - only process files from this bucket
              #   - "--s3-key"
              #   - "PLACEHOLDER_KEY"     # Will be overridden by <PERSON><PERSON> with actual S3 key
              # resources:
              #   requests:
              #     memory: "384Mi"
              #     cpu: "100m"
              #   limits:
              #     memory: "512Mi"
              #     cpu: "400m"
              env:
                - name: GL_ACCOUNT_ID
                  valueFrom:
                    secretKeyRef:
                      name: etransfer-config
                      key: app.gl-account-id
                - name: ACCESS_KEY_ID
                  valueFrom:
                    secretKeyRef:
                      name: gl-recon-aws
                      key: sys.pg.gl.s3.access.key
                - name: SECRET_ACCESS_KEY
                  valueFrom:
                    secretKeyRef:
                      name: gl-recon-aws
                      key: sys.pg.gl.s3.secret.key
                - name: AWS_REGION
                  valueFrom:
                    secretKeyRef:
                      name: gl-recon-aws
                      key: sys.pg.gl.s3.region
