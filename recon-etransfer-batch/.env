# aws s3 section
AWS_ACCESS_KEY_ID = AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY = AWS_SECRET_ACCESS_KEY
S3_BUCKET=your-bucket-name
S3_INPUT_PREFIX=input/
S3_ARCHIVE_PREFIX=archive/
AWS_REGION=your-region
S3_POLL_INTERVAL=30 

# db section
DB_HOST=your-db-host
DB_PORT=5432
DB_NAME=your-db-name
DB_USER=your-db-user
DB_PASSWORD=your-db-password

# datadog section
ENV=dev
SERVICE=s3-ingest
DOMAIN=general-ledger
VERSION=1.0.0
DD_DOGSTATSD_URL=unix:///var/run/datadog/dsd.socket