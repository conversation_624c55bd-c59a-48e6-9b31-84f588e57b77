apiVersion: apps/v1
kind: Deployment
metadata:
  name: transaction-async-listener-deployment
  namespace: pg-ledger-dev
  labels:
    app: transaction-async-listener-v1
    domain: general-ledger
    tags.datadoghq.com/env: dev
    tags.datadoghq.com/service: gl-transaction-async-listener-v1
    tags.datadoghq.com/version: "20250307100023"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: transaction-async-listener-v1
  template:
    metadata:
      annotations:
        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: transaction-async-listener-v1
        admission.datadoghq.com/enabled: "false"
        domain: general-ledger
        tags.datadoghq.com/env: dev
        tags.datadoghq.com/service: gl-transaction-async-listener-v1
        tags.datadoghq.com/version: "20250307100023"
    spec:
      containers:
        - name: transaction-async-listener-container
          image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/ledger-transaction-async-listener:20250307100023
          resources:
            requests:
              memory: "384Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "400m"
          ports:
            - containerPort: 8080
          # Startup probe has higher priority over the two other probe types. Until the Startup Probe succeeds, all the other Probes are disabled.
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 10
            timeoutSeconds: 2
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 20
          # Kubelet uses liveness probes to know when to restart a container. If the liveness probe fails, the kubelet kills the container, and the container is subjected to its restart policy.
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            timeoutSeconds: 30
            periodSeconds: 300
            successThreshold: 1
            failureThreshold: 3
          # Kubernetes makes sure the readiness probe passes before allowing a service to send traffic to the pod. Unlike a liveness probe, a readiness probe doesn’t kill the container. If the readiness probe fails, Kubernetes simply hides the container’s Pod from corresponding Services, so that no traffic is redirected to it.
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            timeoutSeconds: 2
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: TZ
              value: 'America/Toronto'
            - name: SPRING_PROFILES_ACTIVE
              value: 'cloud'
            - name: ACCOUNT_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.schema
            - name: ACCOUNT_READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.read.host
            - name: ACCOUNT_READ_DB_PORT
              value: '5432'
            - name: ACCOUNT_READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.name
            - name: ACCOUNT_READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.username
            - name: ACCOUNT_READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.password
            - name: SASL_USERNAME
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.sasl.username
            - name: SASL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.sasl.password
            - name: KAFKA_SERVER
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.host
            - name: SSL_ENABLE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.ssl.enable
            - name: TOPIC_NAME
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.topic
            - name: ERROR_TOPIC
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.error.topic
            - name: KAFKA_LISTENER_NUMBER
              value: "1"
            - name: STATUS_FAILED_TOPIC
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.failed.status.topic
            - name: CONSUMER_GROUP_ID
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.consumer.group.id
            - name: NO_OF_ATTEMPTS
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.backoff.maxfailure
            - name: BACKOFF_INTERVAL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.backoff.interval
            - name: POLL_INTERVAL_IDLE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.max.poll.idle
            - name: POLL_INTERVAL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.max.poll.interval
            - name: NO_OF_RECORDS_PER_POLL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.max.poll.records
            - name: GL_INITIATE_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.gl.transaction.url
            - name: GL_COMMIT_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.gl.transaction.url
            - name: GL_ROLLBACK_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.gl.transaction.url
            - name: GL_REVERSE_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.gl.transaction.url
            - name: TRANSACTION_API_CONNECTION_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.transaction.api.timeout.connection
            - name: TRANSACTION_API_READ_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.transaction.api.timeout.read
            - name: ASYNC_LISTENER_V2
              value: "false"
            - name: TOPIC_NAME_V2
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.topic.v2
            - name: LOG_LEVEL_ROOT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.root
            - name: LOG_LEVEL_COM_PEOPLESTRUST
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.com.peoplestrust
            - name: LOG_LEVEL_HIBERNATE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.hibernate
            - name: LOG_LEVEL_API_PAYLOAD_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.api.payload
            - name: LOG_LEVEL_PERF_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.perf
            - name: LOG_LEVEL_FLOW_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.flow