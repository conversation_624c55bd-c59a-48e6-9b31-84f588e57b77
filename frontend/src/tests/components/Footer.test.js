import { render, fireEvent } from '@testing-library/react'
import Footer from 'components/Footer'
import findByTestId from 'utils/findByTestId'

const setUp = (props) => {
    render(
        <Footer {...props} />
    )
}


describe('Footer component', () => {

    it('should render without errors', () => {
        setUp()
        const footer = findByTestId('footer')
        expect(footer).toBeInTheDocument()
    })

})