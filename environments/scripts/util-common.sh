# print wrapper to handle DEBUG STATEMENTS
    # ARGUMENTS
    # $1 text to output
function print_debug() {
    if [[ ! -z ${VERBOSE} ]]
    then
        echo -e "DEBUG $1"
    fi
}

# print wrapper to handle verbose
    # ARGUMENTS
    # $1 text to output
function print_info() {
    if [[ -z $1 ]]
    then
        echo
    else
        echo -e "INFO $1"
    fi
}

# Enhanced verbose logging -- green body text
    # ARGUMENTS
    # $1 text to output
    # $2 result to output

function print_green() {
    echo -e "$1 [ \033[38;5;82m$2\033[0m ]"
}

# Enhanced verbose logging -- red body text
    # ARGUMENTS
    # $1 text to output
    # $2 result to output

function print_v_red() {
    if [[ ! -z ${VERBOSE} ]]; then
        echo -e "$1 \033[38;5;196m$2\033[0m"
    fi
}

# Enhanced logging -- red body text
    # ARGUMENTS
    # $1 text to output
    # $2 result to output

function print_red() {
    echo -e "$1 [ \033[38;5;196m$2\033[0m ]"
}

#  Enhanced result logging
    # ARGUMENTS
    # $1 text to output
function print_end() {
    echo -e  "\n\e[30;48;5;82m$1\e[0m"
}

# Maven::Build specific projects
    # ARGUMENTS
    # $1 -- project to build
    # $2 -- build arguments
function maven_build_service() {
  if [[ ${1} == *"listener" ]]; then
    BUILD_PROJECT="${1}"
  else
    BUILD_PROJECT="${1}-api"
  fi
  local BUILD_ARGS=${2}
  
  cmd_with_progress "mvn -T 1C --projects ${BUILD_PROJECT} --also-make clean install ${BUILD_ARGS}" "  Maven build of General Ledger:${PROJECT}"
}

# Maven::Build all projects
    # ARGUMENTS
    # $1 -- build arguments
function maven_build_all() {
  local BUILD_ARGS=${1}

  cmd_with_progress "mvn -T 1C clean install ${BUILD_ARGS}" "  Maven build of General Ledger"
}

# Docker::Authenticate against remote Docker Container Repository
#   ARGUMENTS
#   $1 -- AWS profile for local computer or none for cloud
function docker_auth_ecr() {
    local AWS_PROFILE=${1}

    if [[ ! "$(aws --version)" ]]
    then
        print_debug "See https://psigate.atlassian.net/wiki/spaces/E/pages/906985473/Publishing+Docker+images+to+ECR"
        exit_abormal "aws utility not installed."
    fi

    # authenticate with ECR
    cmd_with_progress "aws ecr get-login-password ${AWS_PROFILE} --region ca-central-1 | docker login --username AWS --password-stdin ${DOCKER_REPO_BASE} > /dev/null 2>&1" "  Authenticating with Amazon ECR"
}

# Docker::Publish docker image on AWS Elastic Container Registry (ECR)
    # ARGUMENTS
    # $1 -- build arguments
    # $2 -- image tag
    # $3 -- AWS profile for local computer or none for cloud
function docker_publish_ecr() {
    local PROJECT_NAME=${1}
    local PROJECT_TAG=${2}
    local AWS_PROFILE=${3}
    
    # authenticate to ECR
    docker_auth_ecr "${AWS_PROFILE}"

    # tag remote build
    cmd_with_progress "docker tag ${PROJECT_NAME}:${PROJECT_TAG} ${DOCKER_REPO_BASE}/${PROJECT_NAME}:${PROJECT_TAG}" "  Creating remote Docker tag for ${PROJECT_NAME}:${PROJECT_TAG}"

    # push to ECR
    cmd_with_progress "docker push ${DOCKER_REPO_BASE}/${PROJECT_NAME}:${PROJECT_TAG}" "  Publishing ${PROJECT_NAME} to Amazon ECR"
}

# Docker::Build docker image for specific projects
    # ARGUMENTS
    # $1 -- project to build
    # $2 -- project tag
function docker_build_service() {
    local PROJECT_NAME=${1}
    local DOCKER_IMAGE_TAG=${2}

    print_green "  Processing ${PROJECT_NAME}" "OK"

    # change directory
    if [[ ${PROJECT_NAME} == *"listener" ]]; then
      cd ${PROJECT_NAME}
    else
      cd ${PROJECT_NAME}-api
    fi


    # extract groupId, artifactId, version and build date
    MAVEN_POM_PROPERTIES_FILE="target/maven-archiver/pom.properties"

    if [[ ! -f ${MAVEN_POM_PROPERTIES_FILE} ]]
    then
        exit_abnormal "${PWD}/${MAVEN_POM_PROPERTIES_FILE} is missing"
    else
        GROUP_ID=$(grep "^groupId=" ${MAVEN_POM_PROPERTIES_FILE} | awk -F"=" '{ print $2 }')
        ARTIFACT_ID=$(grep "^artifactId=" ${MAVEN_POM_PROPERTIES_FILE} | awk -F"=" '{ print $2 }')
        VERSION=$(grep "^version=" ${MAVEN_POM_PROPERTIES_FILE} | awk -F"=" '{ print $2 }')
        BUILD_DATE=$(date --iso-8601=seconds)

        if [[ -z ${GROUP_ID} ||  -z ${ARTIFACT_ID} || -z ${VERSION}  ]]
        then
            print_red "    Load project properties" "FAIL"
            exit_abnormal "${PWD}/${MAVEN_POM_PROPERTIES_FILE} is invalid"
        else
            print_green "    Load project properties" "OK"
        fi
    fi
    #grab the latest image from docker
    docker pull amazoncorretto:17.0.11-alpine
    # RUN apt-get update
    # RUN apt-get upgrade -y
    # build dynamic Dockerfile for project
    # clear out any existing file
    echo > Dockerfile

    # build dynamic file
    cat << DOCKERFILE >> Dockerfile

# first stage, extracts the layers from the Java JAR
FROM amazoncorretto:17.0.11-alpine as builder
COPY target/*.jar application.jar
RUN java -jar -Djarmode=layertools application.jar extract

# second stage, copies over the extracted layers
FROM amazoncorretto:17.0.11-alpine

# Metadata
LABEL app.group_id=${GROUP_ID}
LABEL app.artifact_id=${ARTIFACT_ID}
LABEL app.version=${VERSION}
LABEL app.build_date=${BUILD_DATE}
LABEL app.jre=amazoncorretto-17.0.11-alpine

# Expose application ports (default spring-boot ports)
EXPOSE 8080/tcp

# Expose external volumes

# Application environment variables defaults
#  override with command line when running if different
ENV TZ=America/Toronto
ENV JAVA_OPTS=-Dorg.apache.tomcat.util.buf.UDecoder.ALLOW_ENCODED_SLASH=true -Dorg.apache.catalina.connector.CoyoteAdapter.ALLOW_BACKSLASH=true

# Run application as spring user
RUN addgroup -S spring && adduser -S spring -G spring

WORKDIR /vol/app
COPY --from=builder --chown=spring:spring dependencies/ ./
COPY --from=builder --chown=spring:spring snapshot-dependencies/ ./
COPY --from=builder --chown=spring:spring spring-boot-loader/ ./
COPY --from=builder --chown=spring:spring application/ ./

# Command to execute when container starts
USER spring:spring

ENTRYPOINT exec java \$JAVA_OPTS  org.springframework.boot.loader.JarLauncher
DOCKERFILE

    print_green "    Generate Dockerfile" "OK"

    # Build local docker image
    cmd_with_progress "docker build --tag ${PROJECT_NAME}:${DOCKER_IMAGE_TAG} ." "    Building docker container image"

    # restore filesystem path (in case we are building another)
    cd ..
}


##########################################
#             DOCKER-COMPOSE             #
##########################################

# Docker::Deploy Dockerized-database
    # ARGUMENTS
    #   $1 - project to deploy
    #   NONE
function docker_compose_database() {
    # required environment variables
    local PROJECT_NAME=${1}

    # deploying generic database (no application associated), build ALL schema objects
    database_sql

    # TODO update this to only build DB schema & seed data for needed project
    PG_DB_DDL=${PG_DDL} # global ENV pointing to DDL script to execute
    PG_DB_SEED=${PG_SEED} # global ENV pointing to DDL scrip to to execute

    docker_export_env_local # export all global variables
    
    cmd_with_progress "docker-compose -f ${DOCKER_COMPOSE_POSTGRES} -f ${DOCKER_COMPOSE_POSTGRES_LOCAL} -p ${PROJECT_NAME} up -d" "  Deploying Docker image"

    print_info ""
    print_info ""
    print_info "pgAdmin URL is http://localhost/ (initializing, may need a minute)"
    print_info "  Login:    <EMAIL>"
    print_info "  Password: password"
    print_info ""
    print_info "Common Docker commands"
    print_info "  docker logs ${PROJECT_NAME}_database_1"
    print_info "  docker logs ${PROJECT_NAME}_admin_1"
}

# Docker::Destroy/remove Dockerized-database
    # ARGUMENTS
    #   $1 - project to deploy
    #   NONE
function docker_compose_database_remove() {
    # required environment variables
    local PROJECT_NAME=${1}
    local PROJECT_DATA_FOLDER="${APP_FOLDER}/${PROJECT_NAME}-database"

    # required environment variables
    docker_export_env_local

    cmd_with_progress "docker-compose -f ${DOCKER_COMPOSE_POSTGRES} -f ${DOCKER_COMPOSE_POSTGRES_LOCAL} -p ${PROJECT_NAME} down --volumes" "  Removing Docker image"

    print_info ""
    print_info ""
    print_info "sudo rm -fr ${PROJECT_DATA_FOLDER}"
}

# Docker::Deploy docker container with swagger API specifications
    # ARGUMENTS
    #   NONE
function docker_swagger() {
    WORKSPACE=${PWD}
    
    # sanity check
    if [[ ! -f "${WORKSPACE}/${CUSTOMER_API_SPEC}" ]]
    then
        print_red "  Locate Customer API specification" "FAIL"
    else
        print_green "  Locate Customer API specification" "OK"
    fi

    if [[ ! -f "${WORKSPACE}/${REQUEST_PAYMENT_API_SPEC}" ]]
    then
        print_red "  Locate Request Payment API specification" "FAIL"
    else
        print_green "  Locate Request Payment API specification" "OK"
    fi


    if [[ ! -f "${WORKSPACE}/${PAYMENT_API_SPEC}" ]]
    then
        print_red "  Locate Payment API specification" "FAIL"
    else
        print_green "  Locate Payment API specification" "OK"
    fi

    if [[ ! -f "${WORKSPACE}/${ACCOUNT_API_SPEC}" ]]
    then
        print_red "  Locate Account API specification" "FAIL"
    else
        print_green "  Locate Account API specification" "OK"
    fi

    if [[ ! -f "${WORKSPACE}/${SERVICE_ACCOUNT_API_SPEC}" ]]
    then
        print_red "  Locate Service Account API specification" "FAIL"
    else
        print_green "  Locate Service Account API specification" "OK"
    fi

    if [[ ! -f "${WORKSPACE}/${COMMON_API_SPEC}" ]]
    then
        print_red "  Locate Common API specification" "FAIL"
    else
        print_green "  Locate Common API specification" "OK"
    fi
    
    # export locals
    export WORKSPACE=${PWD}
    export CUSTOMER_API_SPEC
    export REQUEST_PAYMENT_API_SPEC
    export PAYMENT_API_SPEC
    export ACCOUNT_API_SPEC
    export SERVICE_ACCOUNT_API_SPEC
    export COMMON_API_SPEC
    export DOCKER_COMPOSE_SWAGGER
    
    cmd_with_progress "docker-compose -f ${DOCKER_COMPOSE_SWAGGER} -p swagger_draft up -d" "  Deploying Docker image"
}

# Docker::Destroy docker container with swagger API specifications
    # ARGUMENTS
    #   NONE
function docker_swagger_destroy() {
    # export locals
    export CUSTOMER_API_SPEC
    export REQUEST_PAYMENT_API_SPEC
    export PAYMENT_API_SPEC
    export ACCOUNT_API_SPEC
    export SERVICE_ACCOUNT_API_SPEC
    export COMMON_API_SPEC
    export DOCKER_COMPOSE_SWAGGER
    
    docker-compose -f ${DOCKER_COMPOSE_SWAGGER}  -p swagger_draft down --volumes
}



# # Docker::Util to export required database environment variables for remote deployment
    # ARGUMENTS
    #   $1 - project to deploy
function docker_export_env_remote() {
    local PROJECT_NAME=${1}

    # general environment
    docker_export_env ${PROJECT_NAME}

    # export docker repository URL
    export DOCKER_REPO="${DOCKER_REPO_BASE}/"
}

# # Docker::Util to export required database environment variables for remote deployment
    # ARGUMENTS
    #   $1 - project to deploy
function docker_export_env_local() {
    local PROJECT_NAME=${1}

    # general environment
    docker_export_env ${PROJECT_NAME}

    # export empty Docker repo to force load from local
    export DOCKER_REPO=
}

# Docker::Util to export required database environment variables
    # ARGUMENTS
    #   $1 - project to deploy
function docker_export_env() {
    local PROJECT_NAME=${1}

    export PG_DATA_DIR="${PROJECT_DATA_FOLDER}"
    print_debug "PG_DATA_DIR=${PROJECT_DATA_FOLDER}"

    export PG_INIT_DDL_SQL="${PWD}/${PG_DB_DDL}"
    print_debug "PG_INIT_DDL_SQL=${PWD}/${PG_DB_DDL}"

    export PG_INIT_INSERT_SQL="${PWD}/${PG_DB_SEED}"
    print_debug "PG_INIT_INSERT_SQL=${PWD}/${PG_DB_SEED}"

    export PGADMIN_SERVER_JSON="${PWD}/${DOCKER_COMPOSE_PGADMIN_SERVERS}"
    print_debug "PGADMIN_SERVER_JSON=${PWD}/${DOCKER_COMPOSE_PGADMIN_SERVERS}"
    
    # TODO temporary fix
    if [[ ${PROJECT_NAME} == "request-payment" ]]
    then
        export PROJECT_CONTEXT_PATH="request"
    else
        export PROJECT_CONTEXT_PATH=${PROJECT}
    fi

    export PROJECT
    export PROJECT_TAG
    export APP_FOLDER="${APP_FOLDER}/${PROJECT}"
    export JW_FOLDER
}

#
# Utility function to construct SQL DDLs and insert scripts
#
function database_sql() {
        # create target directory to store scripts
        if [[ ! -d "target" ]]
        then
            cmd_with_progress "mkdir target" "  ${PWD}/target : Create temporary folder"
        fi

        # create DDL file for all schemas
        if [[ -f ${PG_DDL} ]]
        then
            rm ${PG_DDL}
        fi

        echo "-- --------------------" >> ${PG_DDL}
        echo "--      CUSTOMER     --" >> ${PG_DDL}
        echo "-- --------------------" >> ${PG_DDL}
        cmd_with_progress "cat ${PG_CUSTOMER_DDL} >> ${PG_DDL}" "  Merging customer SQL into DDL script"
        echo "" >> ${PG_DDL}

        echo "-- --------------------" >> ${PG_DDL}
        echo "--       PAYMENT     --" >> ${PG_DDL}
        echo "-- --------------------" >> ${PG_DDL}
        cmd_with_progress "cat ${PG_PAYMENT_DDL} >> ${PG_DDL}" "  Merging payment SQL into DDL script"
        echo "" >> ${PG_DDL}

        echo "-- --------------------" >> ${PG_DDL}
        echo "--  SERVICE_ACCOUNT  --" >> ${PG_DDL}
        echo "-- --------------------" >> ${PG_DDL}
        cmd_with_progress "cat ${PG_SYSTEM_DDL} >> ${PG_DDL}" "  Merging service account SQL into DDL script"
        echo "" >> ${PG_DDL}

        # create DDL file for all schemas
        if [[ -f ${PG_SEED} ]]
        then
            rm ${PG_SEED}
        fi

        echo "-- --------------------" >> ${PG_DDL}
        echo "--      CUSTOMER     --" >> ${PG_DDL}
        echo "-- --------------------" >> ${PG_DDL}
        cmd_with_progress "cat ${PG_CUSTOMER_SEED} >> ${PG_SEED}" "  Merging customer seed data SQL into data script"
        echo "" >> ${PG_SEED}

        echo "-- --------------------" >> ${PG_DDL}
        echo "--       PAYMENT     --" >> ${PG_DDL}
        echo "-- --------------------" >> ${PG_DDL}
        cmd_with_progress "cat ${PG_PAYMENT_SEED} >> ${PG_SEED}" "  Merging payment seed data SQL into data script"
        echo "" >> ${PG_SEED}

        echo "-- --------------------" >> ${PG_DDL}
        echo "--  SERVICE_ACCOUNT  --" >> ${PG_DDL}
        echo "-- --------------------" >> ${PG_DDL}
        cmd_with_progress "cat ${PG_SYSTEM_SEED} >> ${PG_SEED}" "  Merging service account seed data SQL into data script"
        echo "" >> ${PG_SEED}
}

#
# Utility function to validate preflight setup has been run already
function preflight_check() {
    # check to make sure SETUP has already been executed
    echo -e "\nPreflight checks"
    if [[ ! -f ${SETUP_MARKER} ]]
    then
        print_red "  Filesystem for General Ledger" "FAIL"
        exit_abnormal "Must first run SETUP lifecycle (as root)"
    else
        print_green "  Filesystem for General Ledger" "OK"
    fi
}
#
# Utility function to build out structure of the application
function create_directory_structure() {
    #
    # security folder with all JW* certificates and keys
    #
    #
    # application folder with all application logs and database
    #
    if [[ ! -d ${APP_FOLDER} ]]
    then
        cmd_with_progress "mkdir -p ${APP_FOLDER}" "  ${APP_FOLDER} : Create filesystem folder"
    fi
    cmd_with_progress "chmod -R u=rw+X,g=rw+X,o=rw+X ${APP_FOLDER}" "  ${APP_FOLDER} : Set filesystem permissions"
    cmd_with_progress "chown -R ${SUDO_USER} ${APP_FOLDER}" "  ${APP_FOLDER} : Set filesystem owner"

    if [[ ! -d ${PROJECT_DATA_FOLDER} ]]
    then
        cmd_with_progress "mkdir -p ${PROJECT_DATA_FOLDER}" "  ${PROJECT_DATA_FOLDER} : Create filesystem folder"
    fi
    cmd_with_progress "chmod 0777 ${PROJECT_DATA_FOLDER}" "  ${PROJECT_DATA_FOLDER} : Set filesystem permissions"

}

# Utility function, exit if command fails
function exit_on_error() {
    exit_code=$1
    last_command=${@:2}
    if [ $exit_code -ne 0 ]; then
        >&2 echo -ne "\n\e[38;5;196m ${last_command}\e[0m command failed with exit code ${exit_code}\e[0m"
        exit $exit_code
    fi
}

# abnormal exit (with optional reason)
function exit_abnormal()  {
    echo
    if [[ ! -z $1 ]]
    then
        echo -e "\e[30;48;5;196mERROR\e[0m \e[40;38;5;196m${1}\e[0m"
    fi

    exit 1
}


# Utility function to execute a command with a progress bar
    # ARGUMENTS
    # $1 -- command to execute
    # $2 -- text to display
function cmd_with_progress() {
    # text to rotate through
    spin[0]="."
    spin[1]="|"
    spin[2]="."
    spin[3]="|"

    # command to run (write output to logfile)
    eval $1 >> ${LOG_FILE} 2>&1 &
    pid=$!

    # kill process if script is killed
    trap "kill $pid 2> /dev/null" EXIT

    # Initial text to show
    echo -en "${2} [ "

    # While 'speedtest' is running:
    while kill -0 $pid 2> /dev/null; do
        for i_cmd in "${spin[@]}"
        do
            echo -n ${i_cmd}
            sleep 2
        done
    done

    echo " ]"
    
    # action based on result code
    wait ${pid}
    exit_code=$?
    if [[ ${exit_code} -ne 0 ]]
    then
        # error

        # indicate the result
        echo -ne "[ FAILED\n"

        >&2 echo -e "\n\e[${1} command failed with exit code ${exit_code}n"

        # provide snippet of the output
        tail -10 ${LOG_FILE}

        >&2 echo -e "\nSee ${LOG_FILE} for full output"
        
        exit ${exit_code}

    else
        echo -ne "[ SUCCESS ]\n"
    fi

    # Disable the trap on a normal exit:
    trap - EXIT
}
