import {
    useQuery
} from 'react-query'
import baseQueryOptions from 'constants/baseQueryOptions'
import { getTimeoutHealth } from 'services/account'

const useGetTimeoutHealth = (isSignedIn) => {
    const { data } = useQuery(['getTimeoutHealth'], () => getTimeoutHealth(), {
        ...baseQueryOptions,
        enabled: !!isSignedIn
    })
    
    if (data?.length > 0) {
        return data?.[0]?.totalInternal_ErrorCount
    }

    return !!data && data?.length === 0
}

export default useGetTimeoutHealth
