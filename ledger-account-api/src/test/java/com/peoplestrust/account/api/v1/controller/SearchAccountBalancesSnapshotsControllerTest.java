package com.peoplestrust.account.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.api.v1.service.AccountService;
import com.peoplestrust.account.api.v1.service.ValidationService;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceHistoryResponse;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class SearchAccountBalancesSnapshotsControllerTest {

  private static final String URL = "/v1/ledger/account/balance_search";
  private static final String profileId = UUID.randomUUID().toString();
  @Autowired
  private MockMvc mockMvc;
  @MockBean
  private ValidationService validationService;
  @MockBean
  private AccountService accountService;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;
  private Account account;
  private List<BalanceSnapshotResponse> balanceSnapshotResponseList;

  @BeforeEach
  public void setupBeforeTest() throws Exception {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    UUID interactionId = UUID.randomUUID();
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);

    account = TestUtil.createAccount(profileId);
    balanceSnapshotResponseList = getBalanceSnapshotResponseList();
  }

  private List<BalanceSnapshotResponse> getBalanceSnapshotResponseList() {
    BalanceSnapshotResponse b1 = new BalanceSnapshotResponse();
    List<BalanceSnapshotResponse> balanceList = new ArrayList<>();
    balanceList.add(b1);
    return balanceList;
  }

  @Test
  void searchAccountBalancesSnapshots() throws Exception {
    when(validationService.retrieveLatest48Balance(any(), any(), any())).thenReturn(balanceSnapshotResponseList);

    when(accountService.searchBalanceSnapshots(any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(balanceSnapshotResponseList);

    MvcResult result = this.mockMvc.perform(
            MockMvcRequestBuilders.get(URL + "/" + account.getRefId())
                .headers(headers)
                .param(APICommonUtilConstant.START_TIME, LocalDateTime.now().minusDays(1).toString())
                .param(APICommonUtilConstant.END_TIME, LocalDateTime.now().toString())
                .param(APICommonUtilConstant.OFFSET, "0")
                .param(APICommonUtilConstant.MAX_ITEMS, "25")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    String jsonResponse = result.getResponse().getContentAsString();
    List<RetrieveLedgerAccountBalanceHistoryResponse> balanceHistoryResponse =
        objectMapper.readValue(jsonResponse, List.class);

    assertTrue(balanceHistoryResponse.size() > 0);
  }

  @Test
  void searchAccountBalancesSnapshots_MissingHeader() throws Exception {
    when(validationService.retrieveLatest48Balance(any(), any(), any()))
        .thenReturn(balanceSnapshotResponseList);

    when(accountService.searchBalanceSnapshots(any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(balanceSnapshotResponseList);

    headers.remove(APICommonUtilConstant.AUTHORIZATION);

    MvcResult result =
        this.mockMvc
            .perform(
                MockMvcRequestBuilders.get(URL + "/" + account.getRefId())
                    .headers(headers)
                    .param(
                        APICommonUtilConstant.START_TIME,
                        LocalDateTime.now().minusDays(1).toString())
                    .param(APICommonUtilConstant.END_TIME, LocalDateTime.now().toString())
                    .param(APICommonUtilConstant.OFFSET, "0")
                    .param(APICommonUtilConstant.MAX_ITEMS, "25")
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest())
            .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("MISSING_HEADER", jsonNode.get("error").get("code").asText());
    assertEquals("Authorization", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  void searchAccountBalancesSnapshots_BadToken() throws Exception {
    when(validationService.retrieveLatest48Balance(any(), any(), any()))
        .thenReturn(balanceSnapshotResponseList);

    when(accountService.searchBalanceSnapshots(any(), any(), any(), any(), any(), any(), any()))
        .thenReturn(balanceSnapshotResponseList);

    headers.set(APICommonUtilConstant.AUTHORIZATION, TestUtil.BAD_JWT_TOKEN);

    MvcResult result =
        this.mockMvc
            .perform(
                MockMvcRequestBuilders.get(URL + "/" + account.getRefId())
                    .headers(headers)
                    .param(
                        APICommonUtilConstant.START_TIME,
                        LocalDateTime.now().minusDays(1).toString())
                    .param(APICommonUtilConstant.END_TIME, LocalDateTime.now().toString())
                    .param(APICommonUtilConstant.OFFSET, "0")
                    .param(APICommonUtilConstant.MAX_ITEMS, "25")
                    .contentType(MediaType.APPLICATION_JSON)
                    .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isUnauthorized())
            .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("code").asText());
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("additional_information").asText());

  }

}
