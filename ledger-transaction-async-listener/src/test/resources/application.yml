spring:
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: **********************************************************************
    username: postgres
    password: postgres
    platform: postgres
    driverClassName: org.postgresql.Driver
  jackson:
    default-property-inclusion: NON_NULL
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: true
  jpa:
    properties:
      hibernate:
        jdbc.lob.non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
        order_inserts: true
        order_updates: true
        generate_statistics: false
server:
  tomcat:
    mbeanregistry:
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: info,health,metrics
async-transaction:
  api:
    timetolive: 30000
app:
  name: AsyncTransactionAPI

kafka:
  bootstrap-servers: OUTSIDE://0.0.0.0:9092
  topic: test_topic
  dead:
    letter:
      error:
        topic: transaction_manual_intervention_test
      topic: transaction_failed_test
  group:
    id: transactions_test
  ssl:
    enabled: false
  sasl:
    jaas:
      config:
        username:
        password:
  backoff:
    max_failure: 3
    interval: 2000
  max:
    poll:
      interval:
        idle:
          ms: 900000
        ms: 10000
      records: 1000
transaction:
  api:
    timeout:
      connection: 5000
      read: 5000
    initiate:
      url: http://localhost:8093/v1/ledger/transaction
    commit:
      url: http://localhost:8093/v1/ledger/transaction
    rollback:
      url: http://localhost:8093/v1/ledger/transaction
    reverse:
      url: http://localhost:8093/v1/ledger/transaction