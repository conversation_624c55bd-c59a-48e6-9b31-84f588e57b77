import { lazy } from 'react'

const Profiles = lazy(() => import('pages/Profiles'))
const NotFound = lazy(() => import('pages/NotFound'))
const Profile = lazy(() => import('pages/Profile'))
const Account = lazy(() => import('pages/Account'))
const Transaction = lazy(() => import('pages/Transaction'))
const Search = lazy(() => import('pages/Search'))
const RPS = lazy(() => import('pages/RPS'))
const Response = lazy(() => import('pages/Response'))
const InternalError = lazy(() => import('pages/InternalError'))
const TimeoutError = lazy(() => import('pages/TimeoutError'))

export const routesForTesting = (isSignedIn) => (
  [
    { path: '/', name: 'Test Profiles', Component: isSignedIn ? Profiles : NotFound, exact: true },
    { path: '/profiles/:profileId', name: 'Test Profile Details', Component: isSignedIn ? Profile : NotFound, exact: true },
  ]
)

const getRoutes = (isSignedIn) => (
  [
    { path: '/', name: 'Profiles', Component: isSignedIn ? Profiles : NotFound, exact: true },
    { path: '/:profileId', name: 'Profile Details', Component: isSignedIn ? Profile : NotFound, exact: true },
    { path: '/:profileId/:accountId', name: 'Account Details', Component: isSignedIn ? Account : NotFound, exact: true },
    { path: '/:profileId/:accountId/:instructionId', name: 'Transaction Details', Component: isSignedIn ? Transaction : NotFound, exact: true },
    { path: '/search_transaction', name: 'Search Transaction', Component: isSignedIn ? Search : NotFound, exact: true },
    { path: '/rps', name: 'RPS', Component: isSignedIn ? RPS : NotFound, exact: true },
    { path: '/response', name: 'Response', Component: isSignedIn ? Response : NotFound, exact: true },
    { path: '/internal-error-detail', name: 'Internal Error Detail', Component: isSignedIn ? InternalError : NotFound, exact: true },
    { path: '/timeout-error-detail', name: 'Timeout Error Detail', Component: isSignedIn ? TimeoutError : NotFound, exact: true }
  ]
)

export default getRoutes
