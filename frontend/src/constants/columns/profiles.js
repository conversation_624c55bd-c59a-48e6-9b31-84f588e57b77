import { Link } from 'react-router-dom'

const columns = [
    {
      headerName: '#',
      field: 'id',
      flex: 1,
      maxWidth: 50,
      sortable: false,
      filterable: false,
      renderCell:(index) => index.api.getRowIndex(index.row.ref_id) + 1
    },
    {
      headerName: 'Profiles',
      field: 'legal_name',
      flex: 1,
      renderCell:({ row }) => <Link to={`/${row.ref_id}`}>{row.legal_name}</Link>
    },
    {
      headerName: 'Ref ID',
      field: 'ref_id',
      flex: 1,
    },
    {
      headerName: 'Profile Status',
      field: 'status',
      flex: 1,
    }
]

export default columns