apiVersion: apps/v1
kind: Deployment
metadata:
  name: schedulers-deployment
  namespace: pg-ledger-dev
  labels:
    app: schedulers-v1
    domain: general-ledger
    tags.datadoghq.com/env: dev
    tags.datadoghq.com/service: gl-schedulers-v1
    tags.datadoghq.com/version: "20241112161444"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: schedulers-v1
  template:
    metadata:
      annotations:
        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: schedulers-v1
        admission.datadoghq.com/enabled: "false"
        domain: general-ledger
        tags.datadoghq.com/env: dev
        tags.datadoghq.com/service: gl-schedulers-v1
        tags.datadoghq.com/version: "20241112161444"
    spec:
      containers:
        - name: schedulers-container
          image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/ledger-schedulers:20241112161444
          resources:
            requests:
              memory: "384Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "400m"
          ports:
            - containerPort: 8080
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 30 # after (initialDelaySeconds + periodSeconds * failureThreshold) attempts, restart the pod
          # The kubelet uses readiness probes to know when a container is ready to start accepting traffic. A Pod is considered ready when all of its containers are ready. One use of this signal is to control which Pods are used as backends for Services. When a Pod is not ready, it is removed from Service load balancers.
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 3 # after (initialDelaySeconds + periodSeconds * failureThreshold) attempts, restart the pod
          # The kubelet uses liveness probes to know when to restart a container. For example, liveness probes could catch a deadlock, where an application is running, but unable to make progress. Restarting a container in such a state can help to make the application more available despite bugs.
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 600
            periodSeconds: 300
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: TZ
              value: 'America/Toronto'
            - name: SPRING_PROFILES_ACTIVE
              value: 'cloud'
            - name: READ_WRITE_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.host
            - name: TRANSACTION_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.schema
            - name: READ_WRITE_DB_PORT
              value: '5432'
            - name: READ_WRITE_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.name
            - name: READ_WRITE_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.username
            - name: READ_WRITE_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.password

            - name: READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.read.host
            - name: READ_DB_PORT
              value: '5432'
            - name: READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.name
            - name: READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.username
            - name: READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.password
            - name: ACCOUNT_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.schema
            - name: ACCOUNT_READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.read.host
            - name: ACCOUNT_READ_DB_PORT
              value: '5432'
            - name: ACCOUNT_READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.name
            - name: ACCOUNT_READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.username
            - name: ACCOUNT_READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.password
            - name: BALANCE_SNAPSHOT_SCHEDULE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.balance-snapshot.schedule
            - name: MAXIMUM_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.maximum.pool.size
            - name: DB_CONNECTION_TIME_OUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.connection.timeout
            - name: LOG_LEVEL_ROOT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.root
            - name: LOG_LEVEL_COM_PEOPLESTRUST
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.com.peoplestrust
            - name: LOG_LEVEL_HIBERNATE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.hibernate
            - name: LOG_LEVEL_API_PAYLOAD_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.api.payload
            - name: LOG_LEVEL_PERF_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.perf
            - name: LOG_LEVEL_FLOW_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.flow