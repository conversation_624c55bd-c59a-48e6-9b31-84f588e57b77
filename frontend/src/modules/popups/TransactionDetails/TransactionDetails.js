import { useModal } from 'contexts/ModalProvider'
import Details from 'components/Details'
import TransactionDetails from 'constants/details/transactionDetails'
import MetaDataDetails from 'constants/details/metaData'
import useGetTransactionById from 'hooks/Transactions/useGetTransactionById'
import styled from "styled-components";
import * as S from '../styled'
import { Grid } from '@mui/material'
import useGetMetadata from "hooks/Transactions/useGetMetadata";
import { useEffect } from 'react'

const TransactionDetailsPopup = (props) => {
    const { show, closeModal } = useModal()
    const { profileId, accountId, instructionId, transactionId, paymentRail } =
    props.params;
    const getMetadataDetails = paymentRail === 'INTERNAL'
    const StyledGridItem = styled(Grid)`
        border-right: ${getMetadataDetails ? '1px solid #252525': 'none'};
        width: ${getMetadataDetails ? '50%': 'auto'}
    `;
    const StyledGridItemPart2 = styled(Grid)`
        width: 50%;
    `;
    const {
        data: metaData,
        refetch: refetchMetadata
      } = useGetMetadata(profileId, accountId, instructionId, transactionId);    
    
    const {
        data: transaction,
        refetch: refetchTransactionDetails
    } = useGetTransactionById(props.params)

    useEffect(() => {
        if (props.show === false) return
        refetchMetadata()
        refetchTransactionDetails()
    }, [refetchMetadata, props.show, refetchTransactionDetails])


    return (
        <S.Modal
            show={show}
            handleClose={closeModal}
            title='Transaction Details'
            testId={props.testId || 'transaction-detail-modal'}
        >
            <Grid container spacing={2}>
                <StyledGridItem item size={10}>
                    <Details data={transaction} details={TransactionDetails} />
                </StyledGridItem>
                {getMetadataDetails && 
                <>
                    <S.MetaDataHeader>
                        Additional Details
                    </S.MetaDataHeader>
                    <StyledGridItemPart2 item size={6}>
                        <Details data={metaData} details={MetaDataDetails} />
                    </StyledGridItemPart2>
                </>
                }
            </Grid>
        </S.Modal>
    )
}

export default TransactionDetailsPopup
