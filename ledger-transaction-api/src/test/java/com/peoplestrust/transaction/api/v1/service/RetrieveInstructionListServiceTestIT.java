package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.transaction.api.v1.TransactionApplication;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.persistence.entity.MonetaryUnit;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ContextConfiguration(classes = TransactionApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class RetrieveInstructionListServiceTestIT {


  @Autowired
  private TransactionRepository transactionRepository;

  @Autowired
  private TransactionServiceImpl transactionService;

  @Autowired
  private InstructionRepository instructionRepository;

  @MockBean
  private ValidationService validationService;

  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();


  @Test
  public void getInstructionList() throws Exception {

    Account account = TestUtil.createAccount(accountId, profileId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    Instruction instruction1 = createInstruction1();
    Instruction instruction2 = createInstruction2();
    List<Instruction> instructionList = new ArrayList<>();
    instructionList.add(instruction1);
    instructionList.add(instruction2);

    Instruction savedInstruction1 = transactionService.initiateInstruction(instruction1, profileId, accountId, interactionId);
    Instruction savedInstruction2 = transactionService.initiateInstruction(instruction2, profileId, accountId, interactionId);
    List<Instruction> retrievedInstructionList = transactionService.retrieveInstructionList(profileId, accountId, interactionId);

    assertNotNull(savedInstruction1);
    assertNotNull(savedInstruction2);
    assertNotNull(retrievedInstructionList);
    assertEquals(instructionList.size(), retrievedInstructionList.size());
  }


  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileId), UUID.fromString(accountId)).stream().
        forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }

  private Instruction createInstruction1() {
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = createTransactions();

    Instruction instruction1 = Instruction.builder().instructionRefId(word).accountRefId(accountId).paymentRail(PaymentRailType.EFT)
        .profileRefId(profileId).transactions(transactions).createdDateTime(DateUtils.offset())
        .updatedDateTime(DateUtils.offset()).build();
    instruction1.setTransactions(transactions);

    return instruction1;
  }

  private Instruction createInstruction2() {
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = createTransactions();

    Instruction instruction2 = Instruction.builder().instructionRefId(word).accountRefId(accountId).paymentRail(PaymentRailType.EFT)
        .profileRefId(profileId).transactions(transactions).createdDateTime(DateUtils.offset())
        .updatedDateTime(DateUtils.offset()).build();
    instruction2.setTransactions(transactions);

    return instruction2;
  }

  private List<Transaction> createTransactions() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.DEBIT)
        .paymentCategory(PaymentCategoryType.CREDIT_PUSH).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    Transaction t3 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit(String.valueOf(MonetaryUnit.CAD))
        .acceptanceDateTime(DateUtils.offsetDateTime()).build();

    list.add(t1);
    list.add(t2);
    list.add(t3);
    return list;
  }
}
