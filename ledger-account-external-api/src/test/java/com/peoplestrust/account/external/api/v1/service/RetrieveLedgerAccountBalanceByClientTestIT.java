package com.peoplestrust.account.external.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.account.client.domain.model.RetrieveLedgerAccountBalanceByClientResponse;
import com.peoplestrust.account.external.api.v1.AccountExternalApplication;
import com.peoplestrust.account.external.api.v1.model.Account;
import com.peoplestrust.account.external.api.v1.model.Options;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.MonetaryUnit;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ContextConfiguration(classes = AccountExternalApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class RetrieveLedgerAccountBalanceByClientTestIT {

  private static String profileId = UUID.randomUUID().toString();
  private static String cognitoClientId = RandomString.make(7);
  @Autowired
  ClientAccountService clientAccountService;
  @MockBean
  ReadAccountRepository readAccountRepository;
  private Account account;
  @MockBean
  ValidationService validationService;

  @Autowired
  CacheManager cacheManager;

  String balanceId;

  private Account createAccount() throws Exception {
    UUID uuid = UUID.randomUUID();
    Options options = new Options();
    options.setFundHoldDays(4);
    options.setOverdraftAmount(BigDecimal.valueOf(121.09));
    account =
        Account.builder()
            .refId(uuid.toString())
            .status(AccountStatus.ACTIVE)
            .monetaryUnit("CAD")
            .name("TEST-NAME")
            .options(options)
            .description("TEST_DESCRIPTION")
            .createdDateTime(DateUtils.offset())
            .updatedDateTime(DateUtils.offset())
            .profileId(profileId)
            .build();
    return account;
  }

  private AccountEntity getAccountData(Account account) {
    AccountEntity accountEntity = new AccountEntity();
    OptionsEntity optionsEntity = new OptionsEntity();
    optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
    optionsEntity.setOverdraftAmount(account.getOptions().getOverdraftAmount());
    accountEntity.setCreatedDateTime(DateUtils.offset());
    accountEntity.setUpdatedDateTime(DateUtils.offset());
    accountEntity.setName(account.getName());
    accountEntity.setOptions(optionsEntity);
    accountEntity.setRefId(UUID.fromString(account.getRefId()));
    accountEntity.setDescription(account.getDescription());
    MonetaryUnit monetaryUnit = MonetaryUnit.valueOf(account.getMonetaryUnit());
    accountEntity.setMonetaryUnit(monetaryUnit);
    accountEntity.setStatus(AccountStatus.ACTIVE);
    accountEntity.setProfileId(UUID.fromString(profileId));
    return accountEntity;
  }

  @Test
  public void getAccountBalance() throws Exception {
    RetrieveLedgerAccountBalanceByClientResponse response = getBalanceResponse();
    Account account = createAccount();
    AccountEntity accountEntity = getAccountData(account);
    when(validationService.isProfileAvailable(any(), any(), any())).thenReturn(Boolean.TRUE);
    when(validationService.retrieveBalance(any(), any(), any(), any())).thenReturn(response);
    when(readAccountRepository.findByRefIdAndProfileId(any(), any())).thenReturn(Optional.of(accountEntity));

    String balanceId = profileId + "_" + account.getRefId() + "_" + cognitoClientId;
    RetrieveLedgerAccountBalanceByClientResponse getResponse = clientAccountService.
        retrieveLedgerAccountBalanceForClient(profileId, account.getRefId(), UUID.randomUUID().toString(), balanceId, UUID.randomUUID().toString());
    assertNotNull(getResponse);
  }

  private RetrieveLedgerAccountBalanceByClientResponse getBalanceResponse() {
    return RetrieveLedgerAccountBalanceByClientResponse.builder().
        accountBalance(BigDecimal.valueOf(200))
        .availableBalance(BigDecimal.valueOf(1000))
        .effectiveOn(DateUtils.offsetDateTime())
        .fundHoldAmount(BigDecimal.valueOf(200))
        .prefundReserveAmount(BigDecimal.valueOf(400))
        .overdraftAmount(BigDecimal.valueOf(600)).build();
  }


  @AfterEach
  public void doCleanUpAfterTest() {

  }
}
