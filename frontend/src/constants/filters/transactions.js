import React from 'react'
import ProfilesSearch from 'modules/ProfilesSearch'
import PaymentRailSearch from 'modules/PaymentRailSearch'

export default (props) => [
    {
        label: 'Profiles',
        type: 'select',
        options: props?.profiles?.map?.((profile) => ({
            name: profile?.display_name || '',
            value: profile.ref_id
        })),
        name: 'profile_id',
        render: (
            <ProfilesSearch {...props}/>
        )
    },
    {
        label: 'Instruction ID',
        name: 'instruction-id'
    },
    {
        label: 'Payment Rail',
        type:'select',
        name: 'payment_rail',
        render: (
            <PaymentRailSearch {...props}/>
        )
    },
    {
        label: 'Date From',
        type: 'date',
        name: 'from_date'
    },
    {
        label: 'Date To',
        type: 'date',
        name: 'to_date'
    },
]