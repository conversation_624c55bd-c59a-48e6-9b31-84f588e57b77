import { useState } from "react";
import Table from "components/Table";
import internalForm from "constants/forms/internalForm";
import internalErrorColumns from "constants/columns/internalError";
import useGetInternalError from "hooks/InternalError/useGetInternalError";
import useRequest from "hooks/useRequest";
import moment from "moment";
import { v4 as uuidv4 } from "uuid";
import { toast } from "react-toastify";
import * as S from "./styled";

const InternalError = () => {
  const { data, actions } = useRequest();
  const [formData, setData] = useState({});
  const {
    trigger,
    data: internalData,
    isLoading,
  } = useGetInternalError(formData);

  const onFilter = async (data) => {
    const { start_time, end_time } = data;
    const startDate = new Date(start_time);
    const endDate = new Date(end_time);

    const diffTime = Math.abs(endDate - startDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    const fromDateValid = moment(start_time).isValid();
    const toDateValid = moment(end_time).isValid();

    if (!start_time || !end_time) {
      toast.error("Start/End Date is required");
      return false;
    }

    if (endDate < startDate) {
      toast.error("End date must be greater then Start date");
      return false;
    }
    if (diffDays > 3) {
      toast.error("Gap between Start and End dates must be within 3 days");
      return false;
    }

    if (fromDateValid && !toDateValid) {
      toast.error("Please Enter Valid End Time");
      return false;
    } else if (!fromDateValid && toDateValid) {
      toast.error("Please Enter Valid Start Time");
      return false;
    } else if (
      (start_time !== "" && !fromDateValid) ||
      (end_time !== "" && !toDateValid)
    ) {
      toast.error("Please Enter correct format of date time inputs");
      return false;
    }

    await setData(data);
    trigger();
  };

  const newInternalData = internalData?.error_details?.map?.((item) => {
    return {
      ...item,
      id: uuidv4(),
    };
  });

  return (
    <S.InternalError>
      <Table
        columns={internalErrorColumns}
        data={newInternalData || []}
        total={internalData?.totalInternal_ErrorCount || 0}
        limit={10}
        actions={actions}
        loading={isLoading}
        onFilter={onFilter}
        description={`Total Internal Error Count: ${
          internalData?.totalInternal_ErrorCount || 0
        }`}
        filters={internalForm}
        filterForms={{ grid: 2 }}
        hideFooterPagination
        getRowId={(row) => row.id}
        filterButtonDisabled={isLoading}
        disableSorting
      />
    </S.InternalError>
  );
};

export default InternalError;
