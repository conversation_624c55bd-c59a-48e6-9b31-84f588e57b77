import MenuItem from './MenuItem'
import useGetProfiles from 'hooks/Profile/useGetProfiles'
import routes from 'constants/routes'

import * as S from './styled'

const Sidebar = ({ routes: propsRoutes }) => {
    const data = useGetProfiles()
    const newRoutes = propsRoutes || routes(data)

    return (
        <S.Sidebar data-testid='sidebar'>
            <S.Menu data-testid='menu'>
                {
                    newRoutes?.map(route => (
                        <MenuItem testId='menu-item' route={route} key={route.to} />
                    ))
                }
            </S.Menu>
        </S.Sidebar>
    )
}

export default Sidebar