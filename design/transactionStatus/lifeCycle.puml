@startuml
title Merged Instruction & Transaction Status Lifecycle

!theme plain
skinparam shadowing false
skinparam ArrowLollipopColor #555
skinparam state {
  BackgroundColor<<pending>> #FFF3CD
  BackgroundColor<<success>> #D4EDDA
  BackgroundColor<<failure>> #F8D7DA
  BorderColor #333333
  FontColor #333333
}
skinparam note {
    BackgroundColor #FEFEFE
    BorderColor #333333
}

state "INIT_PENDING" as INIT_PENDING <<pending>>
note left: Instruction & Transaction\nStatus: INIT_PENDING

state "PENDING" as PENDING <<pending>>
note left: Instruction & Transaction\nStatus: PENDING

'-- This is the composite state for the POSTED lifecycle --
state "Instruction Status: POSTED" as POSTED_LIFECYCLE <<success>> {
  state "Transaction Status: POSTED" as T_POSTED
  state "Transaction Status: REVERSED" as T_REVERSED

  [*] -> T_POSTED
  T_POSTED --> T_REVERSED : reverseTransaction()
  T_REVERSED -> T_REVERSED : reverseTransaction()\n(idempotent)
  note right on link
    Original transaction status is updated.
    A new reversal transaction is created
    with REVERSED status.
  end note
}

state "FAILED" as FAILED <<failure>>
note right: Instruction & Transaction\nStatus: FAILED

state "ROLLBACKED" as ROLLBACKED <<failure>>
note right: Instruction & Transaction\nStatus: ROLLBACKED

state "ROLLBACKED_SYSTEM" as ROLLBACKED_SYSTEM <<failure>>
note right: Instruction & Transaction\nStatus: ROLLBACKED_SYSTEM


' --- Transitions ---

[*] --> INIT_PENDING : createInstruction()
INIT_PENDING --> PENDING : (on successful balance check)
INIT_PENDING --> FAILED  : (on failed balance check)

PENDING --> POSTED_LIFECYCLE : commitTransaction()
PENDING --> ROLLBACKED : rollbackTransaction()
PENDING --> ROLLBACKED_SYSTEM : rollbackScheduler()

' Direct entry to POSTED for internal reserves
[*] --> POSTED_LIFECYCLE : initiateReserve()


' --- Idempotent transitions ---
PENDING -> PENDING : commitTransaction()\n(if already POSTED in code)
ROLLBACKED -> ROLLBACKED : rollbackTransaction()\n(idempotent)


' --- End States ---
FAILED --> [*]
ROLLBACKED --> [*]
ROLLBACKED_SYSTEM --> [*]
POSTED_LIFECYCLE --> [*]


legend right
|= Color |= Category |
|<#D4EDDA>| Success / Final |
|<#FFF3CD>| Pending / In-flight |
|<#F8D7DA>| Failure / Terminal |
end legend

@enduml