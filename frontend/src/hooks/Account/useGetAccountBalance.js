import {
    useQuery
} from 'react-query'
import baseQueryOptions from 'constants/baseQueryOptions'
import { getAccountBalance } from 'services/account'

const useGetAccountBalance = (profileId, id, active = true) => {
    const { data } = useQuery(['getAccountBalance', id], () => getAccountBalance(profileId, id), {
        keepPreviousData: true,
        enabled: !!id && active,
        ...baseQueryOptions
    })

    return data

}

export default useGetAccountBalance