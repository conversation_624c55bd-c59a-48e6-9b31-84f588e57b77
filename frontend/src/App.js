import { BrowserRouter as Router } from 'react-router-dom'
import Header from 'components/Header'
import Footer from 'components/Footer'
import Sidebar from 'components/Sidebar'
import Routes from 'navigation'
import Breadcrumb from 'components/Breadcrumb'
import useAuth from 'hooks/useAuth'
import Unauthorized from 'pages/Unauthorized'
import { ToastContainer, toast } from 'react-toastify'
import { COGNITO_GROUPS_LIST } from 'constants/cognitoGroupsTypes'
import * as S from './styled'

const App = () => {
  const { isSignedIn, isAllowed } = useAuth()

  return (
    <Router>
         <S.App>
          <Header />
          {
            (isSignedIn && isAllowed(COGNITO_GROUPS_LIST)) && (
              <Sidebar />
            )
          }
          <S.Content>
              {
                (isSignedIn && isAllowed(COGNITO_GROUPS_LIST)) && (
                  <Breadcrumb />
                )
              }
              {
                (isSignedIn && isAllowed(COGNITO_GROUPS_LIST)) && (
                  <Routes />
                )
              }
              {
                (isSignedIn && !isAllowed(COGNITO_GROUPS_LIST)) && (
                  <Unauthorized />
                )
              }
          </S.Content>
          <Footer />
          <ToastContainer />
        </S.App>
    </Router>
 
  )
}

export default App
