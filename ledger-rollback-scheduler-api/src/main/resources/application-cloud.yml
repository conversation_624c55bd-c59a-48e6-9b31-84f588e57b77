spring:
  datasource:
    transaction-ro:
      url: jdbc:postgresql://${READ_DB_HOST}:${READ_DB_PORT}/${READ_DB_NAME}?currentSchema=${TRANSACTION_DB_SCHEMA}
      username: ${READ_DB_USERNAME}
      password: ${READ_DB_PASSWORD}
      maximumPoolSize: ${MAXIMUM_POOL_SIZE}
      connectionTimeout: ${DB_CONNECTION_TIME_OUT}
  main:
    allow-bean-definition-overriding: true
  jackson:
    default-property-inclusion: non_empty
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: true
  jpa:
    properties:
      hibernate:
        jdbc.lob.non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
        order_inserts: true
        order_updates: true
        generate_statistics: false

scheduler:
  internal:
    rollback:
      endpoint: ${ROLLBACK_INTERNAL_API_URL}
  rollBack:
    transactions: ${ROlL<PERSON>CK_TRANSACTIONS_SCHEDULE}
  rollback:
    expiration:
      amount:
        minutes : ${ROLLBACK_TRANSACTIONS_EXPIRY_TIME}
    instructions:
      fetch:
        limit : ${INSTRUCTIONS_FETCH_LIMIT}
logging:
  level:
    com:
      zaxxer:
        hikari: INFO
      peoplestrust: ${LOG_LEVEL_COM_PEOPLESTRUST}
    root: WARN  # Change this to ERROR for even fewer logs
    org.hibernate.SQL: ${LOG_LEVEL_HIBERNATE}
    APIPayloadLogger: ${LOG_LEVEL_API_PAYLOAD_LOGGER}
    PerfLogger: ${LOG_LEVEL_PERF_LOGGER}
    FlowLogger: ${LOG_LEVEL_FLOW_LOGGER}


