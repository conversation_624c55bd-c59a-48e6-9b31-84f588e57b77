# log into Ecr

aws ecr get-login-password --region ca-central-1 --profile=NON_LEDGER | docker login --username AWS --password-stdin 799455639446.dkr.ecr.ca-central-1.amazonaws.com

# clean the local docker file

docker system prune -af --volumes

# build docker image and push to ECR

cd recon-etransfer-batch
docker build --platform linux/amd64 -t recon-etransfer-batch .
docker image ls | awk '$1 == "recon-etransfer-batch" && $2 == "latest" {print $3}'
docker tag 7c5d0189ad20 799455639446.dkr.ecr.ca-central-1.amazonaws.com/recon-etransfer-batch:20250516093350
docker push 799455639446.dkr.ecr.ca-central-1.amazonaws.com/recon-etransfer-batch:20250516093350

# unit test install

pip install pytest moto freezegun pytest-mock

# unit test verify

pytest
