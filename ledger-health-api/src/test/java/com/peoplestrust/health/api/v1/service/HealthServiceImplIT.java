package com.peoplestrust.health.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.health.api.v1.HealthApplication;
import com.peoplestrust.health.api.v1.config.HealthProperty;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ContextConfiguration(classes = HealthApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class HealthServiceImplIT {

  @Autowired
  HealthProperty healthProperty;

  @Autowired
  HealthService healthService;

  private ObjectMapper objectMapper;

  @Test
  public void testGetHttpInternalErrorWithDateRange_Success() throws Exception {
    LocalDateTime startTime = LocalDateTime.of(2024, 4, 29, 15, 45);
    LocalDateTime endTime = LocalDateTime.of(2024, 5, 1, 15, 45);
    String actual = healthService.getHttpInternalErrorWithDateRange(startTime, endTime);
    objectMapper = new ObjectMapper();
    JsonNode result = objectMapper.readTree(Objects.requireNonNull(actual));
    JsonNode errorMessage1 = result.get("error_details").get(1).get("error_message");
    JsonNode method1 = result.get("error_details").get(1).get("method");
    assertEquals("SERVICE_UNAVAILABLE", errorMessage1.asText());
    assertEquals("TransactionAPI", method1.asText());
    assertEquals(10, result.get("error_details").size());
  }

  @Test
  public void testGetHttpInternalErrorWithDateRange_StartTimeIsNull() {

    LocalDateTime endTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    assertThrows(NullPointerException.class, () -> healthService.getHttpInternalErrorWithDateRange(null, endTime));

  }

  @Test
  public void testGetHttpInternalErrorWithDateRange_EndTimeIsNull() {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    assertThrows(NullPointerException.class, () -> healthService.getHttpInternalErrorWithDateRange(startTime, null));

  }

  @Test
  public void testGetHttpInternalErrorWithDateRange_StartTimeAndEndTImeAreNull() {

    LocalDateTime endTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    assertThrows(NullPointerException.class, () -> healthService.getHttpInternalErrorWithDateRange(null, null));

  }

  //Timeout_Error
  @Test
  public void testGetTimeOutErrorWithDateRange_Success() throws Exception {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 20, 15, 45);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 23, 15, 45);
    String actual = healthService.getTimeOutErrorWithDateRange(startTime, endTime);
    objectMapper = new ObjectMapper();
    JsonNode result = objectMapper.readTree(Objects.requireNonNull(actual));
    assertEquals("34",result.get("totalTimeout").asText());
    assertEquals(10, result.get("error_details").size());
    assertEquals("transaction-async-v1", result.get("error_details").get(0).get("method").asText());

  }

  @Test
  public void testGetTimeOutErrorWithDateRange_StartTimeIsNull() {

    LocalDateTime endTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    assertThrows(NullPointerException.class, () -> healthService.getTimeOutErrorWithDateRange(null, endTime));

  }

  @Test
  public void testGetTimeOutErrorWithDateRange_EndTimeIsNull() {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    assertThrows(NullPointerException.class, () -> healthService.getTimeOutErrorWithDateRange(startTime, null));

  }

  @Test
  public void testGetTimeOutErrorWithDateRange_StartTimeAndEndTimeAreNull() {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    assertThrows(NullPointerException.class, () -> healthService.getTimeOutErrorWithDateRange(null, null));

  }


}
