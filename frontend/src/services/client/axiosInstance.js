import cognitoAuth from './cognito'
import { v4 as uuidv4 } from 'uuid'
import { logout } from 'hooks/useAuth'
import alertMessages from 'constants/alertMessages'
import axios from 'axios'
import { toast } from 'react-toastify'

const createAxiosInstance = (options) => {
  const baseURL = process.env.REACT_APP_API_URL || ''
  const { signInUserSession: { accessToken: { jwtToken }} } = cognitoAuth || {}

  const uuid = uuidv4()
  
  return axios.create({
    baseURL,
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${jwtToken} `,
      'x-pg-interaction-id': `${uuid}`,
      'x-pg-interaction-timestamp': new Date().toISOString()
    }
  })
}

const createRequest = (type, options = {}) => {
  const instance = createAxiosInstance(options)

  instance.interceptors.response.use(undefined, err => {
    const error = err.response

    if (error && error.status === 401) {
      toast.error(error.data.message, {
        autoClose: 1500,
        onClose: () => {
          logout()
        }
      })
    } else if (error && error.status && error.status !== 401) {
      toast.error(error?.data?.error?.additional_information)
    } else {
      toast.error(alertMessages.error)
    }

  })

  return instance[type]
}

const requests = (options = {}) => ({
  get: createRequest('get', options),
  post: createRequest('post', options),
  put: createRequest('put', options),
  delete: createRequest('delete', options),
  patch: createRequest('patch', options)
})

export default requests