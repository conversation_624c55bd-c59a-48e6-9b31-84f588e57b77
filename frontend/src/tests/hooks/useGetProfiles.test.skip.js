import { renderHook } from '@testing-library/react-hooks'
import useGetProfiles from 'hooks/Profile/useGetProfiles'
import profiles from 'data/profiles'
import { fetch_profile } from 'mocks/mswHandlers/profilesHandler'
import { QueryClientProvider } from 'react-query'
import { setupServer } from 'msw/node'
import queryClient from 'services/client/queryClient'

const server = setupServer(fetch_profile)

beforeAll(() => {
    server.listen()
})
afterAll(() => {
    server.close()
})

describe('Should get profiles list', () => {
    it('Should return current profiles list data correctly', async () => {
        server.use(fetch_profile)
        const wrapper = ({ children }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
        const { result, waitForNextUpdate } = renderHook(() => useGetProfiles(), { wrapper })
        await waitForNextUpdate()
        
        expect(result.current).toEqual(profiles)

    })
})