package com.peoplestrust.transaction.async.listener.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.transaction.async.listener.AsyncTransactionListenerApplication;
import com.peoplestrust.transaction.async.listener.config.AsyncTransactionListenerProperty;
import com.peoplestrust.transaction.async.listener.config.KafkaListenerConfig;
import com.peoplestrust.transaction.async.listener.model.AsyncPaymentCategoryType;
import com.peoplestrust.transaction.async.listener.model.AsyncPaymentRailType;
import com.peoplestrust.transaction.async.listener.model.Instruction;
import com.peoplestrust.transaction.async.listener.model.Transaction;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.InstructionStatus;
import com.peoplestrust.transaction.domain.model.PaymentRail;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DeleteTopicsResult;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.client.RestTemplate;

@SpringBootTest
@ContextConfiguration(classes = AsyncTransactionListenerApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class InitiateTransactionAsyncListenerTestIT {

  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();

  @Autowired
  AsyncTransactionListenerServiceImpl asyncTransactionService;

  @MockBean
  RestTemplate restTemplate;

  @Autowired
  KafkaListenerConfig kafkaListenerConfig;

  @Autowired
  AsyncTransactionListenerProperty asyncTransactionListenerProperty;

  @Test
  public void initiateInstructionAsync() throws Exception {
    Instruction instruction = buildInstructionData();
    ResponseEntity<InitiateLedgerTransactionResponse> initiateLedgerTransactionResponse = buildInitiateLedgerTransactionResponse(instruction);
    String topic = "pending_transaction";
    String key = "INIT|" + instruction.getInstructionRefId();
    ConsumerRecord<String, Instruction> record = new ConsumerRecord<>(topic, 0, 123L, key, instruction);
    RecordHeaders headers = new RecordHeaders();
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_OPERATION_ID, APICommonUtilConstant.INITIATE.getBytes());
    when(restTemplate.exchange((String) any(), (HttpMethod) any(), (HttpEntity<?>) any(), (Class<InitiateLedgerTransactionResponse>) any())).thenReturn(
        initiateLedgerTransactionResponse);
    asyncTransactionService.initiateTransaction(record, headers, key);

  }

  private ResponseEntity<InitiateLedgerTransactionResponse> buildInitiateLedgerTransactionResponse(Instruction instruction) {
    InitiateLedgerTransactionResponse initiateLedgerTransactionResponse = new InitiateLedgerTransactionResponse();

    initiateLedgerTransactionResponse.setInstructionRefId(instruction.getInstructionRefId());
    initiateLedgerTransactionResponse.setStatus(InstructionStatus.PENDING);
    initiateLedgerTransactionResponse.setPaymentRail(PaymentRail.valueOf("ETRANSFER"));
    initiateLedgerTransactionResponse.setTransactions(null);

    ResponseEntity<InitiateLedgerTransactionResponse> initiateLedgerTransactionResponse1 = new ResponseEntity<InitiateLedgerTransactionResponse>(
        initiateLedgerTransactionResponse,
        HttpStatus.OK);

    return initiateLedgerTransactionResponse1;
  }


  private Instruction buildInstructionData() {
    String word = "TEST_INST" + RandomString.make(7);
    List<Transaction> transactions = buildTransactionData();
    Instruction instruction = Instruction.builder().instructionRefId(word).paymentRail(AsyncPaymentRailType.ETRANSFER).transactions(transactions).build();
    return instruction;
  }

  private List<Transaction> buildTransactionData() {
    List<Transaction> list = new ArrayList<>();
    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString())
        .paymentCategory(AsyncPaymentCategoryType.COMPLETE_PAYMENT).amount(new BigDecimal(100)).monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString())
        .paymentCategory(AsyncPaymentCategoryType.SEND_PAYMENT).amount(new BigDecimal(100)).monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    list.add(t1);
    list.add(t2);
    return list;
  }


  @AfterEach
  public void doCleanUp() {
    AdminClient client = AdminClient.create(kafkaListenerConfig.kafkaAdminClient().getConfigurationProperties());
    DeleteTopicsResult deleteTopicsResult = client.deleteTopics(Collections.singletonList(asyncTransactionListenerProperty.getKafkaTopic()));
    DeleteTopicsResult deleteTopicsResult1 = client.deleteTopics(Collections.singletonList(asyncTransactionListenerProperty.getDeadLetterTopic()));
  }
}
