{"name": "gl-ui", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "1.x", "@emotion/react": "^11.10.0", "@emotion/styled": "^11.10.0", "@fortawesome/fontawesome-svg-core": "^6.1.2", "@fortawesome/free-solid-svg-icons": "^6.1.2", "@fortawesome/react-fontawesome": "^0.2.0", "@material-ui/core": "^4.12.4", "@mui/icons-material": "^5.10.3", "@mui/lab": "^5.0.0-alpha.99", "@mui/material": "^5.10.0", "@mui/styled-engine": "^5.10.1", "@mui/x-data-grid": "^5.15.2", "@mui/x-date-pickers": "^5.0.6", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "amazon-cognito-auth-js": "^1.3.3", "axios": "^0.27.2", "buffer": "^6.0.3", "crypto": "^1.0.1", "crypto-browserify": "^3.12.0", "env-cmd": "^10.1.0", "formik": "^2.2.9", "jsonwebtoken": "^8.5.1", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "msw": "^0.48.2", "qs": "^6.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-query": "^3.39.2", "react-router-dom": "^6.3.0", "react-scripts": "4.0.3", "react-toastify": "8.0.0", "stream": "^0.0.2", "styled-components": "^5.3.5", "use-react-router-breadcrumbs": "^4.0.1", "util": "^0.12.4", "uuid": "^9.0.0", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "start:qa": "env-cmd -f .env.qa npm run start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "build:qa": "env-cmd -f .env.qa npm run build", "build:staging": "env-cmd -f .env.staging npm run build", "build:prod": "env-cmd -f .env.prod npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "resolutions": {"@babel/core": "^7.22.0"}, "overrides": {"@babel/core": "^7.22.0"}}