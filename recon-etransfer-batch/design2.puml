@startuml
actor User

box "Application Layer" #LightBlue
  participant "main.py" as Main
  participant "metrics.py" as Metrics
  participant "Environment Variables" as Env
end box

box "AWS Cloud" #LightYellow
  participant "AWS S3" as S3
end box

box "Database" #LightGreen
  participant "PostgreSQL DB" as DB
end box

box "Monitoring" #LightPink
  participant "Datadog Agent" as Datadog
end box

box "Web Server" #LightGray
  participant "Flask Health Server" as Flask
end box

User -> Main: Start application
Main -> Env: Load environment variables (load_dotenv())
Main -> Metrics: init_datadog()
Metrics -> Datadog: Initialize statsd client
Metrics -> Datadog: Emit service.started metric
Main -> Flask: Start health server thread
Flask -> Flask: Serve /actuator/health endpoint

loop Every POLL_INTERVAL seconds
  Main -> S3: list_csv_files() [list_objects_v2]
  S3 --> Main: Return CSV file list
  alt Files found
    Main -> Metrics: gauge("s3.files_found", count)
    loop For each file
      Main -> S3: read_csv_from_s3(key) [get_object]
      S3 --> Main: Return CSV content
      Main -> Main: Parse CSV with pandas
      alt Parse success
        Main -> DB: connect_to_postgres()
        DB --> Main: Connection established
        Main -> DB: check_records_in_db() [SELECT query]
        DB --> Main: Return sample rows
        Main -> S3: move_to_archive(key) [copy_object]
        S3 --> Main: Copy confirmation
        Main -> S3: [delete_object]
        S3 --> Main: Delete confirmation
        Main -> Metrics: increment("s3.file_processed.success")
      else Parse failure
        Main -> S3: move_to_failed(key) [copy_object]
        S3 --> Main: Copy confirmation
        Main -> S3: [delete_object]
        S3 --> Main: Delete confirmation
        Main -> Metrics: increment("s3.file_processed.failure")
      end
      Main -> Metrics: timing("file.processing_time", duration)
    end
  else No files found
    Main -> Metrics: gauge("s3.files_found", 0)
    Main -> Main: Sleep for POLL_INTERVAL
  end
end

note right of Flask
  Health endpoint returns:
  {"status": "UP"}, 200
end note

@enduml
