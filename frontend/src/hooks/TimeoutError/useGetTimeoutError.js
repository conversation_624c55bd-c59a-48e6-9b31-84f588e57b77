import baseQueryOptions from 'constants/baseQueryOptions'
import useLazyQuery from 'hooks/useLazyQuery'
import { getTimeoutErrorDetails } from 'services/timeout'

const useGetTimeoutError = (props) => {
    const [trigger, { data, isLoading }] = useLazyQuery(['getTimeoutError', props], () => getTimeoutErrorDetails(props), {
        keepPreviousData: true,
        ...baseQueryOptions
    })

    return { trigger, data }

}

export default useGetTimeoutError