import {
    useQuery
} from 'react-query'
import baseQueryOptions from 'constants/baseQueryOptions'
import { getAccountBalanceSnapshots } from 'services/account'

const useGetAccountBalanceSnapshot = (props) => {
    const { data, isLoading, refetch } = useQuery(['getAccountBalanceSnapshot', props], () => getAccountBalanceSnapshots(props), {
        enabled: !!props,
        ...baseQueryOptions
    })

    return { data, isLoading, refetch }

}

export default useGetAccountBalanceSnapshot