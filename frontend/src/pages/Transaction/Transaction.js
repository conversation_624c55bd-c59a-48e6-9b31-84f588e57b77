import Table from 'components/Table'
import useGetTransaction from 'hooks/Transactions/useGetTransactions'
import TransactionColumns from 'constants/columns/transactions'
import { useParams } from 'react-router-dom'
import * as S from './styled'


const Transactions = () => {
    const params = useParams()
    const instruction = useGetTransaction(params)
    const transactions = instruction?.transactions

    return (
         <S.Transactions>
            <h2 data-testid='page-title'>Transactions</h2>

            <Table 
                columns={TransactionColumns}
                data={transactions}
                total={transactions?.length}
                getRowId={(row) => row.transaction_ref_id}
            />
        </S.Transactions>
      
    )
}

export default Transactions