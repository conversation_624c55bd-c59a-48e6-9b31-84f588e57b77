import { useMutation } from "react-query";
import { createUpdateMetadata } from "services/transaction";

const useCreateUpdateMetadata = (onSuccess, onError) => {
  return useMutation(
    ({ profileId, accountId, instructionRefId, transactionRefId, payload }) =>
      createUpdateMetadata({
        profileId,
        accountId,
        instructionRefId,
        transactionRefId,
        metadata: payload,
      }),
    {
      onSuccess,
      onError,
    }
  );
};

export default useCreateUpdateMetadata;
