package com.peoplestrust.profile.api.v1.service;

import com.peoplestrust.profile.api.v1.ProfileApplication;
import com.peoplestrust.profile.api.v1.ProfileTestUtil;
import com.peoplestrust.profile.api.v1.model.Profile;
import com.peoplestrust.profile.persistence.entity.ProfileEntity;
import com.peoplestrust.profile.persistence.repository.write.ProfileRepository;
import com.peoplestrust.util.api.common.exception.InvalidFieldException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ContextConfiguration(classes = ProfileApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class CreateProfileServiceTestIT {

    @MockBean
    private ProfileRepository profileRepository;

    @Autowired
    ProfileServiceImpl profileService;

    @Test
    public void insertTest() throws Exception {

        Profile profile = ProfileTestUtil.createProfile();
        when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        // when
        Profile returnProfile = profileService.createProfile(profile);
        // then
        assertEquals(profile.getRefId(), returnProfile.getRefId());
        assertEquals(profile.getCrmId(), returnProfile.getCrmId());
        assertEquals(profile.getDisplayName(), returnProfile.getDisplayName());
        assertEquals(profile.getLegalName(), returnProfile.getLegalName());
    }

    @Test
    public void insertSPCTest() throws Exception {

        Profile profile = ProfileTestUtil.createSPCProfile();

        when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        // when
        Profile returnProfile = profileService.createProfile(profile);

        // then
        assertEquals(profile.getRefId(), returnProfile.getRefId());
        assertEquals(profile.getCrmId(), returnProfile.getCrmId());
        assertEquals(profile.getDisplayName(), returnProfile.getDisplayName());
        assertEquals(profile.getLegalName(), returnProfile.getLegalName());
    }

    @Test
    public void CreateDuplicate_failed() {
        Profile profile = ProfileTestUtil.createProfile();
        // Create a nested exception hierarchy
        SQLException sqlException = new SQLException("duplicate key value violates unique constraint \"profile_ref_id_key\"");
        DataIntegrityViolationException dataIntegrityViolationException = new DataIntegrityViolationException("Data integrity violation", sqlException);

        // Mock the behavior of profileRepository to throw DataIntegrityViolationException
        when(profileRepository.save(any(ProfileEntity.class))).thenThrow(dataIntegrityViolationException);

        // attempt to create the profile should throw InvalidFieldException
        Exception ex = assertThrows(InvalidFieldException.class, () -> {
            profileService.createProfile(profile);
        });
    }


}
