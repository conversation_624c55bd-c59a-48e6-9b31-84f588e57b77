import { useCallback } from "react";
import CreateProfile from "./createProfile";
import ModalProvider from "contexts/ModalProvider";
import useCreateProfile from "hooks/Profile/useCreateProfile";
import useUpdateProfile from "hooks/Profile/useUpdateProfile";
import createProfileForm from "constants/forms/createProfile";
import updateProfileForm from "constants/forms/updateProfile";
import schema, { updateSchema } from "./schema";

const CreateProfileContainer = (props) => {
  const mutation = useCreateProfile();
  const updateMutation = useUpdateProfile();
  const handleSubmit = useCallback(
    (values) => {
      if (props.data && props.edit) {
        updateMutation.mutate({
          ...values,
          id: props?.data?.ref_id,
        });
      } else {
        mutation.mutate(values);
      }
    },
    [props.data]
  );

  return (
    <ModalProvider
      data={props.data}
      edit={props.edit}
      schema={props.data && props.edit ? updateSchema : schema}
      fields={props.edit && props.data ? updateProfileForm : createProfileForm}
      handleClose={props.handleClose}
      show={props.show}
      handleSubmit={handleSubmit}
    >
      <CreateProfile {...props} data={mutation.data} onReset={mutation.reset} />
    </ModalProvider>
  );
};

export default CreateProfileContainer;
