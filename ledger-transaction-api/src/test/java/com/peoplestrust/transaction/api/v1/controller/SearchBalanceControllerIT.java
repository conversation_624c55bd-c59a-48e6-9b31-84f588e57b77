package com.peoplestrust.transaction.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class SearchBalanceControllerIT {

  private static final String URL_INTERNAL_BALANCE = "/v1/internal/transaction/balance/search/";
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  @MockBean
  private ReadBalanceRepository readBalanceRepository;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;

  static {
    System.setProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  public void searchBalanceSnapshotTest() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    headers.add(APICommonUtilConstant.OVERDRAFT_AMOUNT, "25000");

    String profileId = UUID.randomUUID().toString();
    String accountId = UUID.randomUUID().toString();
    List<BalanceEntity> balanceEntity = TestUtil.getBalanceEntityLst(profileId, accountId);
    when(readBalanceRepository.findByAccountRefIdAndProfileRefId(any(), any(), any())).thenReturn(balanceEntity);

    this.mockMvc.perform(
            MockMvcRequestBuilders.get(URL_INTERNAL_BALANCE + accountRefId)
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();
  }

  @Test
  public void searchBalanceSnapshot_InvalidProfileId() throws Exception {
    headers.set(APICommonUtilConstant.HEADER_PROFILE_ID, "invalid-profile-id");

    this.mockMvc.perform(
            MockMvcRequestBuilders.get(URL_INTERNAL_BALANCE + accountRefId)
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  public void searchBalanceSnapshot_InvalidAccountId() throws Exception {
    headers.set(APICommonUtilConstant.HEADER_ACCOUNT_ID, "invalid-account-id");

    this.mockMvc.perform(
            MockMvcRequestBuilders.get(URL_INTERNAL_BALANCE + "invalid-account-id")
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  public void searchBalanceSnapshot_InvalidMaxResponseItems() throws Exception {
    this.mockMvc.perform(
            MockMvcRequestBuilders.get(URL_INTERNAL_BALANCE + accountRefId)
                .headers(headers)
                .param(APICommonUtilConstant.MAX_ITEMS, "300")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  public void searchBalanceSnapshot_StartTimeAfterEndTime() throws Exception {
    LocalDateTime startTime = LocalDateTime.now().plusDays(1);
    LocalDateTime endTime = LocalDateTime.now();

    this.mockMvc.perform(
            MockMvcRequestBuilders.get(URL_INTERNAL_BALANCE + accountRefId)
                .headers(headers)
                .param(APICommonUtilConstant.START_TIME, startTime.toString())
                .param(APICommonUtilConstant.END_TIME, endTime.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

}
