import { renderHook } from '@testing-library/react-hooks'
import useCreateProfile from 'hooks/Profile/useCreateProfile'
import profiles from 'data/profiles'
import { create_profile, mockSingleProfile } from 'mocks/mswHandlers/profilesHandler'
import { QueryClientProvider } from 'react-query'
import { setupServer } from 'msw/node'
import queryClient from 'services/client/queryClient'

const server = setupServer(create_profile)

beforeAll(() => {
    server.listen()
})
afterAll(() => {
    server.close()
})


describe('Should create profiles', () => {
    it('Should return current profiles list data correctly', async () => {
        server.use(create_profile)
        const wrapper = ({ children }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
        const { result, waitForNextUpdate } = renderHook(() => useCreateProfile(), { wrapper })
        const mutation = result.current
        mutation.mutate(mockSingleProfile)

        await waitForNextUpdate()
        
        expect(result.current.data).toEqual([...profiles, mockSingleProfile])

    })
})