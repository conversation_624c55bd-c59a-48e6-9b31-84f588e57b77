<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>ledger-parent-api</artifactId>
    <groupId>com.peoplestrust</groupId>
    <version>1.0-SNAPSHOT</version>
    <relativePath>../ledger-parent-api/pom.xml</relativePath>
  </parent>
  <artifactId>ledger-profile-api</artifactId>
  <name>Ledger::Profile::API</name>
  <dependencies>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>ledger-profile-domain</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>ledger-profile-persistence</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>common-api</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-redis</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-cache</artifactId>
      <version>${spring.boot.version}</version>
    </dependency>
  </dependencies>

</project>