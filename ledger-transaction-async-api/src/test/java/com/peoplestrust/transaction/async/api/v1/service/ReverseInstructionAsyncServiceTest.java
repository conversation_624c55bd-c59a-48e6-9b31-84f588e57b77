package com.peoplestrust.transaction.async.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.transaction.async.api.v1.config.AsyncTransactionProperty;
import com.peoplestrust.transaction.async.api.v1.model.AsyncPaymentCategoryType;
import com.peoplestrust.transaction.async.api.v1.model.AsyncPaymentRailType;
import com.peoplestrust.transaction.async.api.v1.model.Instruction;
import com.peoplestrust.transaction.async.api.v1.model.Transaction;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class ReverseInstructionAsyncServiceTest {

  @InjectMocks
  private AsyncTransactionServiceImpl asyncTransactionService;

  @Mock
  private AsyncTransactionProperty asyncTransactionProperty;

  @Mock
  private KafkaTemplate kafkaTemplate;

  @Mock
  ValidationService validationService;

  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();

  @Test
  public void reverseTransaction() throws Exception {
    Instruction instruction = createInstruction();
    String transRefId = instruction.getTransactions().get(0).getTransactionRefId();
    AccountEntity account = createAccount();
    when(asyncTransactionProperty.getKafkaTopic()).thenReturn("test-topic");
    SendResult<String, Object> sendResult = mock(SendResult.class); // Create a mock SendResult
    CompletableFuture<SendResult<String, Object>> future = CompletableFuture.completedFuture(sendResult); // Complete the future with your SendResult

    when(kafkaTemplate.send((ProducerRecord) any())).thenReturn(future);
    Boolean response = asyncTransactionService.reverseTransaction(instruction.getInstructionRefId(), transRefId, profileId, accountId, interactionId);

    assertEquals(response, Boolean.TRUE);
  }

  private Instruction createInstruction() {
    UUID uuid = UUID.randomUUID();
    List<Transaction> transactions = createTransactions();

    Instruction instruction = Instruction.builder().instructionRefId("TEST_INS" + UUID.randomUUID().toString()).paymentRail(AsyncPaymentRailType.ETRANSFER)
        .transactions(transactions)
        .build();
    instruction.setTransactions(transactions);
    return instruction;
  }


  private List<Transaction> createTransactions() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).paymentCategory(AsyncPaymentCategoryType.COMPLETE_PAYMENT)
        .amount(new BigDecimal(100)).monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).paymentCategory(AsyncPaymentCategoryType.SEND_PAYMENT)
        .amount(new BigDecimal(100)).monetaryUnit("CAD")
        .acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    list.add(t1);
    list.add(t2);
    return list;
  }


  private AccountEntity createAccount() {
    OptionsEntity options = createOptions();
    AccountEntity account = AccountEntity.builder().name("CryptoFin Peer2Peer Settlement Acct").description("For all peer to peer transfers")
        .monetaryUnit(com.peoplestrust.account.persistence.entity.MonetaryUnit.CAD).options(options).status(AccountStatus.ACTIVE)
        .refId(UUID.fromString(accountId)).profileId(UUID.fromString(profileId)).build();
    return account;
  }

  private OptionsEntity createOptions() {
    OptionsEntity options = OptionsEntity.builder().overdraftAmount(BigDecimal.valueOf(100000)).fundHoldDays(5)
        .build();
    return options;
  }

}
