# General Ledger Repository

## Overview

The General Ledger (GL) system is a financial management application built to handle accounting transactions, account management, and financial reporting. It consists of multiple microservices built with Java/Spring Boot and a React frontend.

### Key Components

- **Backend Services**: Java 17 with Spring Boot 3.1.x
  - Profile API: Manages ledger profiles
  - Account API: Handles account management
  - Transaction API: Processes financial transactions
  - Health API: System health monitoring
  - Schedulers: Background job processing

- **Frontend**: React-based UI with Material UI components

- **Infrastructure**:
  - PostgreSQL: Primary database (master-slave replication)
  - Redis: Caching and session management
  - Kafka: Event streaming platform
  - AWS: Deployment environment

## Prerequisites

- Docker Desktop
- Java 17
- Maven 3.9+
- Node.js 18+
- npm or yarn

## Local Setup

### Database and Infrastructure

**Start all services with a single command:**

```bash
docker-compose up -d
```

Or start individual components as needed:

**PostgreSQL Setup:**

```bash
# Start master database (read-write)
docker-compose -f docker-compose.yml up -d postgres

# Start slave database (read-only)
docker-compose -f docker-compose.yml up -d postgres-slave

# Run database migrations
docker-compose -f docker-compose.yml up migrate
```

**Redis Setup:**

```bash
# Start Redis server
docker-compose -f docker-compose.yml up -d redis

# Start Redis Commander UI
docker-compose -f docker-compose.yml up -d redis-commander
```

Access Redis Commander UI at http://localhost:9090

**Kafka Setup:**

```bash
# Start Kafka and Kowl UI
docker-compose -f kafka-kowl.yml up -d
```

Access Kafka Kowl UI at http://localhost:8080

### Backend Services

Build and run the backend services using Maven:

```bash
# Build all services
mvn clean install

# Run a specific service (example: ledger-profile-api)
cd ledger-profile-api
mvn spring-boot:run
```

### Frontend

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

Access the frontend at http://localhost:3000

## Development

### Project Structure

```
├── common-api/            # Common API utilities
├── common-domain/         # Shared domain models
├── common-logger/         # Logging utilities
├── environments/          # Environment configurations
├── frontend/              # React frontend application
├── ledger-*-api/          # API modules for different services
├── ledger-*-domain/       # Domain modules
├── ledger-*-persistence/  # Database persistence modules
└── ledger-parent/         # Parent POM configuration
```

### Building for Different Environments

**Backend:**

```bash
mvn clean package -P<profile>
```

**Frontend:**

```bash
# Build for QA
npm run build:qa

# Build for staging
npm run build:staging

# Build for production
npm run build:prod
```

## CI/CD Pipeline

### Generating the Pipeline

In order to facilitate maintenance on the pipeline, it is recommended to always make changes to the template first instead of the pipeline directly.

1. Install [gomplate](https://docs.gomplate.ca/)
2. Execute the template:

```bash
gomplate -f environments/scripts/bitbucket-pipelines.tmpl \
  -d input=environments/scripts/input.yaml \
  -o bitbucket-pipelines.yml
```

### Adding a New Service

To add/remove a service, add/remove it in the `environments/scripts/input.yaml` file and regenerate the pipeline using the command above.

## Security

Run security analysis with Snyk:

```bash
./security-analyze.sh
```

## Deployment

### Frontend Deployment to AWS S3

```bash
# Build for target environment
npm run build:<env>

# Configure AWS credentials
export AWS_ACCESS_KEY_ID="your-key"
export AWS_SECRET_ACCESS_KEY="your-secret"
export AWS_SESSION_TOKEN="your-token"

# Deploy to S3 bucket
aws --region ca-central-1 s3 sync ./build s3://<bucket-name> --delete
```

## Troubleshooting

- **Database Connection Issues**: Verify PostgreSQL containers are running with `docker ps`
- **API Errors**: Check application logs for detailed error messages
- **Frontend Build Failures**: Ensure Node.js version compatibility (v18 recommended)

