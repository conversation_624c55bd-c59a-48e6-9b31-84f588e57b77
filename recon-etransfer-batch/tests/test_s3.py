import os
import re
from batch_job import list_csv_files, move_to_archive, s3


def test_list_csv_files(s3_bucket):
    files = list_csv_files()
    assert sorted(files) == ["input/file1.csv", "input/file2.csv"]


def test_move_to_archive(s3_bucket, frozen_time):
    key = "input/file1.csv"
    move_to_archive(key)

    # original object should be gone
    objs = s3.list_objects_v2(
        Bucket=os.environ["S3_BUCKET"], Prefix="input/"
    ).get("Contents", [])
    remaining = [o["Key"] for o in objs]
    assert key not in remaining

    # exactly one object now under archive/, with expected prefix
    objs = s3.list_objects_v2(
        Bucket=os.environ["S3_BUCKET"], Prefix="archive/"
    )["Contents"]
    archived_keys = [o["Key"] for o in objs]
    assert len(archived_keys) == 1
    assert archived_keys[0].startswith("archive/file1_")
    assert archived_keys[0].endswith(".csv")

    # optional: validate timestamp format YYYYMMDDTHHMMSSZ
    ts_part = re.search(r'file1_(\d{8}T\d{6}Z)\.csv', archived_keys[0]).group(1)
    assert re.fullmatch(r"\d{8}T\d{6}Z", ts_part)
