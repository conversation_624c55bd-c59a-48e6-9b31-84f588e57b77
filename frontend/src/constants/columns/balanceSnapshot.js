import moment from "moment-timezone";
import amountFormat from "utils/amountFormat";

const columns = [
  {
    headerName: "Date and Time",
    field: "effective_on",
    flex: 1,
    valueGetter: ({ row }) => {
      if (!row.effective_on) return "N/A";
      const formatted = moment
        .utc(row.effective_on)
        .tz("America/New_York")
        .format("YYYY MMM DD HH:mm:ss");
      return formatted;
    },
  },
  {
    headerName: "Balance",
    field: "balance",
    valueGetter: ({ row }) => amountFormat(row.total_amount),
    flex: 1,
  },
];

export default columns;
