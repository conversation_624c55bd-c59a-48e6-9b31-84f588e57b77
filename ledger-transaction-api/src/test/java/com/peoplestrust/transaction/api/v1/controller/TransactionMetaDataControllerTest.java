package com.peoplestrust.transaction.api.v1.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.service.TransactionService;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.Metadata;
import com.peoplestrust.transaction.domain.model.Metadata.TicketTypeEnum;
import com.peoplestrust.transaction.domain.model.RetrieveMetadata;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import com.peoplestrust.util.api.common.util.JsonUtil;
import com.peoplestrust.util.api.common.util.Messages;
import org.springframework.core.env.Environment;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.UUID;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class TransactionMetaDataControllerTest {

  private static final String URL = "/v1/ledger/transaction/{instruction_ref_id}/{transaction_ref_id}/metadata";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();
  private static final String instructionRefId = UUID.randomUUID().toString();
  private static final String transactionRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  @MockBean
  private TransactionService transactionService;

  private HttpHeaders headers;

  static {
    System.setProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION, "true");
  }

  @BeforeEach
  public void setupBeforeTest() {
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.AUTHORIZATION_HEADER, TestUtil.JWT_TOKEN);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  public void updateTransactionMetaDataTest() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    // Mocking the service method
    when((transactionService).updateTransactionMetadata(
        any(String.class),
        any(String.class),
        any(String.class),
        any(String.class),
        any(String.class),
        any())).thenReturn(true);;

    // Create a sample metadata object
    Metadata metadata = new Metadata();
    metadata.setNotes("Sample note");
    metadata.setTicketId("12345");
    metadata.setTicketType(TicketTypeEnum.fromValue("JIRA"));

    // Perform the PUT request
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated());
  }

  @Test
  public void updateTransactionMetaDataTest_TicketIdTooLong() throws Exception {
    Metadata metadata = new Metadata();
    metadata.setTicketId("12345678901234567890123456"); // 26 characters (too long)
    metadata.setNotes("Valid note");

    // Perform the PUT request and expect a 400 Bad Request due to invalid ticketId length
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void updateTransactionMetaDataTest_NotesTooShort() throws Exception {
    Metadata metadata = new Metadata();
    metadata.setTicketId("12345");
    metadata.setNotes("abcd"); // Less than 5 characters

    // Perform the PUT request and expect a 400 Bad Request due to short notes length
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  public void updateTransactionMetaDataTest_NoTicketIdOrNotes() throws Exception {
    Metadata metadata = new Metadata();
    // Neither ticketId nor notes are set

    // Perform the PUT request and expect a 400 Bad Request due to missing both ticketId and notes
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @ParameterizedTest
  @CsvSource({
      "12345678901234567890123456,Sample note", // ticketId too long
      "12345,abcd", // notes too short
      "12345678901234567890123456,abcd" // both ticketId too long and notes too short
  })
  public void updateTransactionMetaDataTest_InvalidInput_ShouldReturnBadRequest(String ticketId, String notes) throws Exception {
    Metadata metadata = new Metadata();
    metadata.setTicketId(ticketId);
    metadata.setNotes(notes);

    // Perform the PUT request and expect a 400 Bad Request due to invalid input
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @ParameterizedTest
  @NullAndEmptySource
  public void updateTransactionMetaDataTest_MissingTicketIdAndNotes_ShouldReturnBadRequest(String emptyValue) throws Exception {
    Metadata metadata = new Metadata();
    metadata.setTicketId(emptyValue);
    metadata.setNotes(emptyValue);

    // Perform the PUT request and expect a 400 Bad Request due to missing both ticketId and notes
    this.mockMvc.perform(MockMvcRequestBuilders.put(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .content(JsonUtil.toString(metadata))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  // Tests for GET Method

  @Test
  public void getTransactionMetaDataTest_Success() throws Exception {
    // Create a sample RetrieveMetadata object
    RetrieveMetadata retrieveMetadata = new RetrieveMetadata();
    retrieveMetadata.setTicketId("12345");
    retrieveMetadata.setTicketType(RetrieveMetadata.TicketTypeEnum.JIRA);
    retrieveMetadata.setNotes("Sample note");

    // Mocking the service layer to return the sample metadata
    when(transactionService.getTransactionMetadata(
        any(String.class),
        any(String.class),
        any(String.class),
        any(String.class),
        any(String.class)))
        .thenReturn(retrieveMetadata);

    // Perform the GET request
    this.mockMvc.perform(MockMvcRequestBuilders.get(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.ticket_id").value("12345"))
        .andExpect(jsonPath("$.ticket_type").value("JIRA"))
        .andExpect(jsonPath("$.notes").value("Sample note"));
  }

  @Test
  public void getTransactionMetaDataTest_ResourceNotFound() throws Exception {
    // Mocking the service layer to throw a ResourceNotFoundException
    when(transactionService.getTransactionMetadata(
        any(String.class),
        any(String.class),
        any(String.class),
        any(String.class),
        any(String.class)))
        .thenThrow(new ResourceNotFoundException(Messages.TRANSACTION_METADATA_NOT_FOUND));

    // Perform the GET request and expect 404 Not Found
    this.mockMvc.perform(MockMvcRequestBuilders.get(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isNotFound());
  }

  @Test
  public void getTransactionMetaDataTest_InvalidUUID() throws Exception {
    // Mock invalid UUID by setting a malformed profileRefId
    String invalidProfileRefId = "invalid-uuid";

    headers.set(APICommonUtilConstant.HEADER_PROFILE_ID, invalidProfileRefId);

    // Perform the GET request and expect 400 Bad Request due to invalid UUID format
    this.mockMvc.perform(MockMvcRequestBuilders.get(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }
  @Test
  public void getTransactionMetaDataTest_ticket_type_null() throws Exception {

    // Create a sample RetrieveMetadata object
    RetrieveMetadata retrieveMetadata = new RetrieveMetadata();
    retrieveMetadata.setNotes("Sample note");

    // Mocking the service layer to return the sample metadata
    when(transactionService.getTransactionMetadata(
        any(String.class),
        any(String.class),
        any(String.class),
        any(String.class),
        any(String.class)))
        .thenReturn(retrieveMetadata);

    // Perform the GET request
    this.mockMvc.perform(MockMvcRequestBuilders.get(URL, instructionRefId, transactionRefId)
            .headers(headers)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.notes").value("Sample note"));
  }
}
