apiVersion: apps/v1
kind: Deployment
metadata:
  name: transaction-async-deployment
  namespace: pg-ledger-dev
  labels:
    app: transaction-async-v1
    domain: general-ledger
    tags.datadoghq.com/env: dev
    tags.datadoghq.com/service: gl-transaction-async-v1
    tags.datadoghq.com/version: "20250307095902"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: transaction-async-v1
  template:
    metadata:
      annotations:
        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: transaction-async-v1
        admission.datadoghq.com/enabled: "false"
        domain: general-ledger
        tags.datadoghq.com/env: dev
        tags.datadoghq.com/service: gl-transaction-async-v1
        tags.datadoghq.com/version: "20250307095902"
    spec:
      containers:
        - name: transaction-async-container
          image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/ledger-transaction-async:20250307095902
          resources:
            requests:
              memory: "384Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "400m"
          ports:
            - containerPort: 8080
          # Startup probe has higher priority over the two other probe types. Until the Startup Probe succeeds, all the other Probes are disabled.
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 10
            timeoutSeconds: 2
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 20
          # Kubelet uses liveness probes to know when to restart a container. If the liveness probe fails, the kubelet kills the container, and the container is subjected to its restart policy.
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            timeoutSeconds: 30
            periodSeconds: 300
            successThreshold: 1
            failureThreshold: 3
          # Kubernetes makes sure the readiness probe passes before allowing a service to send traffic to the pod. Unlike a liveness probe, a readiness probe doesn’t kill the container. If the readiness probe fails, Kubernetes simply hides the container’s Pod from corresponding Services, so that no traffic is redirected to it.
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            timeoutSeconds: 2
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: TZ
              value: 'America/Toronto'
            - name: DISABLE_COGNITO_JWT_VERIFICATION
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.jwt.token.check.disable
            - name: SPRING_PROFILES_ACTIVE
              value: 'cloud'
            - name: ACCOUNT_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.schema
            - name: ACCOUNT_READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.read.host
            - name: ACCOUNT_READ_DB_PORT
              value: '5432'
            - name: ACCOUNT_READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.name
            - name: ACCOUNT_READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.username
            - name: ACCOUNT_READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: account-database
                  key: sys.database.password
            - name: SASL_USERNAME
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.sasl.username
            - name: SASL_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.sasl.password
            - name: KAFKA_SERVER
              valueFrom:
                secretKeyRef:
                  name: kafka
                  key: sys.kafka.host
            - name: SSL_ENABLE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.ssl.enable
            - name: TOPIC_NAME
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.topic
            - name: REDIS_DB_CLUSTER
              valueFrom:
                secretKeyRef:
                  name: redis-cache
                  key: sys.database.cluster
            - name: REDIS_READ_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.redis.read.timeout
            - name: REDIS_CONNECTION_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.redis.connection.timeout
            - name: MAXIMUM_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.maximum.pool.size
            - name: DB_CONNECTION_TIME_OUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.connection.timeout
            - name: ASYNC_TRANSACTION_V2
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.async.transaction.producer.v2
            - name: TOPIC_NAME_V2
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.kafka.topic.v2
            - name: LOG_LEVEL_ROOT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.root
            - name: LOG_LEVEL_COM_PEOPLESTRUST
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.com.peoplestrust
            - name: LOG_LEVEL_HIBERNATE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.hibernate
            - name: LOG_LEVEL_API_PAYLOAD_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.api.payload
            - name: LOG_LEVEL_PERF_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.perf
            - name: LOG_LEVEL_FLOW_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.flow