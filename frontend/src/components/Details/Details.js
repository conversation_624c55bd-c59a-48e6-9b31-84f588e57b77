import * as S from './styled'
import _ from 'lodash'

const defaultEmptyText = 'N/A'

const descriptionText = (data, field) => {
    if (typeof field === 'function') {
        return field(data)
    }

    const value = _.get(data, field) ?? defaultEmptyText;
    return value;
}

const DetailsItem = ({ title, field, data, testId}) => {
    return (
        <S.DetailsItem data-testid={testId || 'details-item'}>
            <b data-testid='details-item-title'>
                {title}:
            </b>
            <p data-testid='details-item-description'>
                {descriptionText(data, field)}
            </p>
        </S.DetailsItem>
    );
};

const Details = ({ data, details}) => {
    return (
        <S.Details data-testid='details'>
            {
                details?.map(item => {
                    const children = item.children
                    if (children) {
                        return (
                            <S.DetailsGroup data-testid='details-group' key={item.key}>
                                <h3 data-testid='details-group-title'>
                                    {item.title}
                                </h3>
                                {
                                    children.map(child => (
                                        <DetailsItem 
                                            key={child.key}
                                            title={child.title}
                                            field={`${item.key}.${child.key}`}
                                            data={data}
                                            testId='details-child-item' 
                                        />
                                    ))
                                }
                            </S.DetailsGroup>
                        )
                    }
                    
                    return (
                        <DetailsItem key={item.key} title={item.title} field={item.key} data={data} />
                    )
                })
            }
        </S.Details>
    )
}

export default Details