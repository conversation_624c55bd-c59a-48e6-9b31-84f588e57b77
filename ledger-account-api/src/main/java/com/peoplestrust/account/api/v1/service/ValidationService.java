package com.peoplestrust.account.api.v1.service;

import com.peoplestrust.account.api.v1.config.AccountProperty;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceResponse;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Service
@Component
public class ValidationService {

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    AccountProperty accountProperty;


    /**
     * Adapter to check profile status from Profile API
     *
     * @param refId
     * @param interactionId
     * @return
     */
    public boolean isProfile(String refId, String interactionId) throws Exception {
        // build HTTP headers
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        log.debug("invoking Profile API::Retrieve status for ref_id = {}, interaction_id = {}", refId, interactionId);

        ResponseEntity<Boolean> profile =
                restTemplate.exchange(
                        accountProperty.getProfileInternalUrl() + "/" + refId,
                        HttpMethod.GET,
                        entity,
                        Boolean.class);

        log.debug("result = {}", profile.getBody().booleanValue());
        return profile.getBody().booleanValue();
    }

    /**
     * Adapter to get account related balances from Transaction API
     *
     * @param refId           Unique ID that identifies the account
     * @param profileRefId    Unique ID that identifies the profile
     * @param interactionId   Unique ID
     * @param overdraftAmount Overdraft amount related to account
     * @return RetrieveLedgerAccountBalanceResponse
     */
    public RetrieveLedgerAccountBalanceResponse retrieveBalance(String refId, String profileRefId, String interactionId, BigDecimal overdraftAmount) {

        // build HTTP headers
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
        headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
        headers.set(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
        headers.set(APICommonUtilConstant.OVERDRAFT_AMOUNT, overdraftAmount.toString());

        HttpEntity<String> entity = new HttpEntity<>(headers);

        log.debug("invoking Transaction API::Retrieve balance for ref_id = {}, interaction_id = {}", refId, interactionId);

        ResponseEntity<RetrieveLedgerAccountBalanceResponse> transaction =
                restTemplate.exchange(
                        accountProperty.getTransactionInternalUrl() + "/" + refId,
                        HttpMethod.GET,
                        entity,
                        RetrieveLedgerAccountBalanceResponse.class);

        log.debug("result = {}", transaction.getBody());
        return transaction.getBody();
    }

    /**
     * Adapter to retrieve past 48 balances for a specific account
     *
     * @param refId         unique account identifier
     * @param profileRefId  unique profile identifier
     * @param interactionId unique id
     * @return List of BalanceSnapshotResponse
     */
    public List<BalanceSnapshotResponse> retrieveLatest48Balance(String refId, String profileRefId, String interactionId) throws Exception {

        // build HTTP headers
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
        headers.set(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);

        HttpEntity<String> entity = new HttpEntity<>(headers);

        log.debug("invoking Transaction API::Retrieve balance for accountRef_id = {}, interaction_id = {},profileId={} ",
                refId, interactionId, profileRefId);

        ResponseEntity<BalanceSnapshotResponse[]> balanceList = restTemplate.exchange(
                accountProperty.getTransactionInternalUrl() + "/" + "all/" + refId,
                HttpMethod.GET,
                entity, BalanceSnapshotResponse[].class);

        log.debug("result = {}", balanceList.getBody());
        return List.of(balanceList.getBody());
    }

    public List<BalanceSnapshotResponse> searchBalanceSnapshots(String refId, String profileRefId, String interactionId, LocalDateTime startTime, LocalDateTime endTime, Integer offset, Integer maxResponseItems) throws Exception {

        // build HTTP headers
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        headers.set(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
        headers.set(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);

        HttpEntity<String> entity = new HttpEntity<>(headers);

        // build the query string
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(accountProperty.getTransactionInternalUrl() + "/search/" + refId)
                .queryParam(APICommonUtilConstant.START_TIME, startTime)
                .queryParam(APICommonUtilConstant.END_TIME, endTime)
                .queryParam(APICommonUtilConstant.OFFSET, offset)
                .queryParam(APICommonUtilConstant.MAX_ITEMS, maxResponseItems);

        log.debug("invoking Transaction API::search balanceSnapshot for accountRef_id = {}, interaction_id = {}, profileId={} , startTime={}, endTime={}, offSet={}, maxItem={}, url={}",
                refId, interactionId, profileRefId, startTime, endTime, offset, maxResponseItems, uriBuilder.toUriString());

        ResponseEntity<BalanceSnapshotResponse[]> balanceList = restTemplate.exchange(
                uriBuilder.toUriString(),
                HttpMethod.GET,
                entity, BalanceSnapshotResponse[].class);

        log.debug("search balanceSnapshot result = {}", balanceList.getBody());
        return List.of(balanceList.getBody());
    }
}