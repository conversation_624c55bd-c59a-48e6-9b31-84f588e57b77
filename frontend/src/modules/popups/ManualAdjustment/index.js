import { useCallback, useRef, useState } from 'react'
import ManualAdjustment from './ManualAdjustment'
import ModalProvider from 'contexts/ModalProvider'
import useManualAdjustment from 'hooks/Account/useManualAdjustment'
import useCreateUpdateMetadata from 'hooks/Transactions/useCreateUpdateMetadata'
import useAuth from 'hooks/useAuth'
import { GL_ADMINISTRATION } from 'constants/cognitoGroupsTypes'
import schema from './schema'
import { toast } from "react-toastify";
import queryClient from '../../../services/client/queryClient'
import alertMessages from 'constants/alertMessages'

const ManualAdjustmentContainer = (props) => {
    const { isAllowed } = useAuth()
    const isAdmin = isAllowed(GL_ADMINISTRATION)
    const [saveDisabled, setSaveDisabled] = useState(true)
    const cleanForm = useRef(null)

    const closePopUpAndRefreshBalance = async () => {
      await queryClient.refetchQueries(['getAccountBalance'], { active: true })
      toast.success(alertMessages.manualCorrection)
      if (cleanForm?.current) cleanForm.current()
      props.handleClose()
    }

    const onError = (error) => {
      console.error("Error updating Additional Details:", error);
    };
    const onSuccessfulManualAdjustment = async (response, values) => {
      if (!response) {
        return onError()
      }
      const { ticket_id, ticket_type, notes } = values || {};
      if ((!ticket_id || !ticket_type) && !notes) {
        return await closePopUpAndRefreshBalance()
      }
      const payload = {
        ...(ticket_id && { ticket_id }),
        ...(ticket_type && { ticket_type }),
        ...(notes ? { notes } : {}),
      };

      const variables = {
          profileId: props?.data?.profileId,
          accountId: props?.data?.accountId,
          instructionRefId: response?.instruction_ref_id,
          transactionRefId: response?.transactions[0].transaction_ref_id,
          payload,
        };

      // Proceed with mutation if validation passes
      createMetadataMutation?.mutate(variables);
      await closePopUpAndRefreshBalance()
    }

    const createMetadataMutation = useCreateUpdateMetadata(undefined, onError)
    const manualAdjustmentMutation = useManualAdjustment(onSuccessfulManualAdjustment)

    const handleSubmit = useCallback((values, {resetForm}) => {
        cleanForm.current = resetForm
        if (!isAdmin || saveDisabled) return

        const { ticket_id, ticket_type, notes } = values || {};
    
        const hasOneTicketField =
          (!!ticket_id && !ticket_type) || (!ticket_id && !!ticket_type);
  
        // Validation logic with error messages
        if (hasOneTicketField) {
          toast.error(
            "Both Ticket ID and Ticket Type are required when providing ticket information."
          );
          return false;
        }
  
        // validation for ticket_id length
        if (ticket_id && ticket_id.length > 25) {
          toast.error("Ticket ID must be at most 25 characters long.");
          return false;
        }
  
        // validation for notes length
        if (notes && (notes.length < 5 || notes.length > 256)) {
          toast.error("Notes must be between 5 and 256 characters long.");
          return false;
        }

        manualAdjustmentMutation.mutate({
          ...values,
          profileId: props?.data?.profileId,
          accountId: props?.data?.accountId
      })
    }, [props.data, isAdmin, manualAdjustmentMutation, saveDisabled])

    return (
        <ModalProvider
            edit={props.edit}
            schema={schema}
            handleClose={props.handleClose}
            show={props.show}
            handleSubmit={handleSubmit}
        >
            <ManualAdjustment
              {...props}
              saveDisabled={saveDisabled}
              setValidation={setSaveDisabled}
            />
        </ModalProvider>
    )
}

export default ManualAdjustmentContainer;
