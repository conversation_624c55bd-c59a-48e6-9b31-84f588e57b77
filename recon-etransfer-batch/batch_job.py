import boto3
import pandas as pd
import psycopg2
import logging
import os
import time
from dotenv import load_dotenv
import threading
from flask import request, Flask
import datetime
import botocore.exceptions

# Custom metrics wrapper
from metrics import init_datadog, increment, gauge, timing, event

# Load env vars
load_dotenv()

# Logging config
formatter = logging.Formatter(
    '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
handler = logging.StreamHandler()
handler.setFormatter(formatter)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.addHandler(handler)

# Environment var helper
def require_env(name):
    value = os.getenv(name)
    if not value:
        raise EnvironmentError(f"Missing required environment variable: {name}")
    return value

# S3 config
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
S3_BUCKET = os.getenv('S3_BUCKET')
INPUT_PREFIX = os.getenv('S3_INPUT_PREFIX', 'input/')
FAILED_PREFIX = os.getenv('S3_FAILED_PREFIX', 'failed/')
ARCHIVE_PREFIX = os.getenv('S3_ARCHIVE_PREFIX', 'archive/')
AWS_REGION = os.getenv('AWS_REGION')
POLL_INTERVAL = int(os.getenv('S3_POLL_INTERVAL', 30))  # seconds

# DB config
DB_HOST = os.getenv('READ_DB_HOST')
DB_PORT = os.getenv('READ_DB_PORT')
DB_NAME = os.getenv('READ_DB_NAME')
DB_USER = os.getenv('READ_DB_USERNAME')
DB_PASSWORD = os.getenv('READ_DB_PASSWORD')

# Init S3 client
s3 = boto3.client('s3', region_name=AWS_REGION,
                  aws_access_key_id=AWS_ACCESS_KEY_ID,
                  aws_secret_access_key=AWS_SECRET_ACCESS_KEY)

def list_csv_files():
    response = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix=INPUT_PREFIX)
    return [obj['Key'] for obj in response.get('Contents', []) if obj['Key'].endswith('.csv')]

def read_csv_from_s3(key):
    logger.info(f"Reading S3 file: {key}")
    obj = s3.get_object(Bucket=S3_BUCKET, Key=key)
    try:
        return pd.read_csv(obj['Body'])
    except pd.errors.ParserError as e:
        logger.warning(f"ParserError on {key}: {e}. Trying fallback...")
        obj['Body'].seek(0)
        try:
            return pd.read_csv(obj['Body'], error_bad_lines=False, warn_bad_lines=True, engine='python')
        except Exception as inner_e:
            logger.error(f"Failed to parse CSV after fallback: {key}: {inner_e}")
            raise

def move_to_archive(key):
    if not key:
        logger.error("❌ No S3 key provided for archiving.")
        return

    try:
        filename = os.path.basename(key)
        timestamp = datetime.datetime.utcnow().strftime("%Y%m%dT%H%M%SZ")
        name, ext = os.path.splitext(filename)
        archive_filename = f"{name}_{timestamp}{ext}"
        archive_prefix = ARCHIVE_PREFIX if ARCHIVE_PREFIX.endswith('/') else ARCHIVE_PREFIX + '/'
        archive_key = f"{archive_prefix}{archive_filename}"

        logger.info(f"📦 Archiving file: s3://{S3_BUCKET}/{key} → s3://{S3_BUCKET}/{archive_key}")
        s3.copy_object(Bucket=S3_BUCKET, CopySource={'Bucket': S3_BUCKET, 'Key': key}, Key=archive_key)
        s3.delete_object(Bucket=S3_BUCKET, Key=key)
        logger.info(f"✅ Successfully archived to {archive_key}")
    except botocore.exceptions.ClientError as e:
        logger.error(f"❌ Failed to archive {key}: {e.response['Error']['Message']}")
    except Exception as e:
        logger.exception(f"❌ Unexpected error archiving {key}")

def move_to_failed(key):
    filename = key.split('/')[-1]
    failed_key = f"{FAILED_PREFIX}/{filename}"
    logger.info(f"🚫 Moving failed file {key} → {failed_key}")
    s3.copy_object(Bucket=S3_BUCKET, CopySource={'Bucket': S3_BUCKET, 'Key': key}, Key=failed_key)
    s3.delete_object(Bucket=S3_BUCKET, Key=key)

def connect_to_postgres():
    return psycopg2.connect(
        host=DB_HOST,
        port=DB_PORT,
        dbname=DB_NAME,
        user=DB_USER,
        password=DB_PASSWORD
    )

def check_records_in_db(df, conn):
    cursor = conn.cursor()
    try:
        cursor.execute('SELECT * FROM "transactions" LIMIT 3;')
        sample = cursor.fetchall()
        logger.info(f"✅ DB sample check succeeded. Sample rows: {sample}")
        increment("db.check.success")
    except Exception as e:
        logger.error("❌ DB sample query failed", exc_info=True)
        increment("db.check.failure")
        raise
    finally:
        cursor.close()

def process_file(key):
    start_time = time.time()
    try:
        df = read_csv_from_s3(key)
        conn = connect_to_postgres()
        check_records_in_db(df, conn)
        conn.close()
        move_to_archive(key)
        logger.info(f"✅ Finished processing {key}")
        increment("s3.file_processed.success", tags=[f"filename:{os.path.basename(key)}"])
    except Exception as e:
        logger.error(f"❌ Error processing {key}: {e}", exc_info=True)
        increment("s3.file_processed.failure", tags=[f"filename:{os.path.basename(key)}"])
        try:
            move_to_failed(key)
        except Exception as archive_err:
            logger.error(f"❌ Failed to move file to failed/: {archive_err}")
    finally:
        duration = time.time() - start_time
        timing("file.processing_time", duration, tags=[f"filename:{os.path.basename(key)}"])

def run_monitor():
    logger.info("Starting S3 input folder monitor...")
    while True:
        try:
            files = list_csv_files()
            if files:
                logger.info(f"📁 Found {len(files)} new file(s)")
                gauge("s3.files_found", len(files), tags=[f"bucket:{S3_BUCKET}"])
                for key in files:
                    process_file(key)
            else:
                logger.debug("No new files found.")
        except Exception as e:
            logger.error("Error during polling loop", exc_info=True)

        time.sleep(POLL_INTERVAL)

# Health check server
app = Flask(__name__)

@app.before_request
def suppress_health_check_logs():
    if request.path == '/actuator/health':
        logging.getLogger('werkzeug').setLevel(logging.ERROR)

@app.route("/actuator/health")
def health():
    return {"status": "UP"}, 200

def start_health_server():
    app.run(host="0.0.0.0", port=8080)

if __name__ == '__main__':
    init_datadog()
    threading.Thread(target=start_health_server, daemon=True).start()
    run_monitor()
