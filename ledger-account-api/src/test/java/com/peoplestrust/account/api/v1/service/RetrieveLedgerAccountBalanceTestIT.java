package com.peoplestrust.account.api.v1.service;

import com.peoplestrust.account.api.v1.AccountApplication;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceResponse;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ContextConfiguration(classes = AccountApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class RetrieveLedgerAccountBalanceTestIT {

    private static String profileId = UUID.randomUUID().toString();
    @Autowired
    AccountService accountService;
    @MockBean
    ReadAccountRepository readAccountRepository;
    private Account account;
    @MockBean
    ValidationService validationService;

    @Test
    public void getAccountBalance() throws Exception {
        RetrieveLedgerAccountBalanceResponse response = TestUtil.getBalanceResponse();
        when(validationService.retrieveBalance(any(), any(), any(), any())).thenReturn(response);

        Account account = TestUtil.createAccount(UUID.randomUUID().toString());

        AccountEntity accountEntity = new AccountEntity();
        accountEntity.setProfileId(UUID.fromString(profileId));
        accountEntity.setRefId(UUID.fromString(UUID.randomUUID().toString()));
        accountEntity.setStatus(AccountStatus.ACTIVE);

        OptionsEntity optionsEntity = new OptionsEntity();
        optionsEntity.setOverdraftAmount(new BigDecimal(100.00));
        accountEntity.setOptions(optionsEntity);

        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));
        when(validationService.retrieveBalance(any(), any(), any(), any())).thenReturn(response);


        RetrieveLedgerAccountBalanceResponse getResponse = accountService.
                retrieveLedgerAccountBalance(profileId, account.getRefId(), UUID.randomUUID().toString());
        assertNotNull(getResponse);
    }


    @AfterEach
    public void doCleanUpAfterTest() {
        log.trace("cleanup - start");

        log.trace("clean up - end");
    }
}
