package com.peoplestrust.transaction.api.v1.mapper;

import static com.peoplestrust.util.api.common.util.Utils.asByteArray;

import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.transaction.api.v1.model.Balance;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.domain.model.*;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.transaction.domain.model.CommitLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.InitiateLedgerPrefundReserveRequest;
import com.peoplestrust.transaction.domain.model.InitiateLedgerPrefundReserveResponse;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionRequest;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.InstructionStatus;
import com.peoplestrust.transaction.domain.model.InternalPaymentCategory;
import com.peoplestrust.transaction.domain.model.InternalPaymentRail;
import com.peoplestrust.transaction.domain.model.LedgerTransaction;
import com.peoplestrust.transaction.domain.model.PaymentCategory;
import com.peoplestrust.transaction.domain.model.PaymentRail;
import com.peoplestrust.transaction.domain.model.RetrieveLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.RetrieveLedgerTransactionsResponse;
import com.peoplestrust.transaction.domain.model.TransactionStatus;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.ValueMapping;
import org.mapstruct.ValueMappings;
import org.mapstruct.factory.Mappers;

/**
 * Interface for Transaction mapper
 */
@Mapper(componentModel = "spring", imports = {MapperUtil.class})
public interface TransactionMapper {

    TransactionMapper INSTANCE = Mappers.getMapper(TransactionMapper.class);

    /**
     * Enum mapping from payment rail dto to domain payment rail
     *
     * @param paymentRailType
     * @return
     */
    @ValueMappings({
            @ValueMapping(source = "EFT", target = "EFT"),
            @ValueMapping(source = "ETRANSFER", target = "ETRANSFER"),
            @ValueMapping(source = "WIRES", target = "WIRES"),
            @ValueMapping(source = "VISA", target = "VISA"),
            @ValueMapping(source = "INTERNAL", target = "INTERNAL"),
            @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
    })
    PaymentRail paymentRailDtoToDomain(PaymentRailType paymentRailType);

    /**
     * Enum mapping from payment rail dto to domain internal payment rail
     *
     * @param paymentRailType
     * @return
     */
    @ValueMappings({
            @ValueMapping(source = "INTERNAL", target = "INTERNAL"),
            @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
    })
    InternalPaymentRail paymentRailDtoToInternalDomain(PaymentRailType paymentRailType);

    /**
     * Enum mapping from payment category dto to domain payment rail
     *
     * @param paymentCategoryType
     * @return
     */
    @ValueMappings({
            @ValueMapping(source = "DEBIT_PULL", target = "DEBIT_PULL"),
            @ValueMapping(source = "CREDIT_PUSH", target = "CREDIT_PUSH"),
            @ValueMapping(source = "SEND_PAYMENT", target = "SEND_PAYMENT"),
            @ValueMapping(source = "COMPLETE_PAYMENT", target = "COMPLETE_PAYMENT"),
            @ValueMapping(source = "ALIAS_DEPOSIT_PAYMENT", target = "ALIAS_DEPOSIT_PAYMENT"),
            @ValueMapping(source = "ACCOUNT_NUMBER_DEPOSIT_PAYMENT", target = "ACCOUNT_NUMBER_DEPOSIT_PAYMENT"),
            @ValueMapping(source = "FULFILL_REQUEST_FOR_PAYMENT", target = "FULFILL_REQUEST_FOR_PAYMENT"),
            @ValueMapping(source = "REVERSAL", target = "REVERSAL"),
            @ValueMapping(source = "CORRECTION", target = "CORRECTION"),
            @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
    })
    PaymentCategory paymentCategoryDtoToDomain(PaymentCategoryType paymentCategoryType);

    /**
     * Enum mapping from payment category dto to domain internal payment category
     *
     * @param paymentCategoryType
     * @return
     */
    @ValueMappings({
            @ValueMapping(source = "RESERVE", target = "PREFUND_RESERVE"),
            @ValueMapping(source = "CORRECTION", target = "CORRECTION"),
            @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
    })
    InternalPaymentCategory paymentCategoryDtoToInternalDomain(PaymentCategoryType paymentCategoryType);

    /**
     * Enum mapping from domain payment rail to payment rail dto
     *
     * @param paymentCategoryType
     * @return
     */
    @ValueMappings({
            @ValueMapping(source = "EFT", target = "EFT"),
            @ValueMapping(source = "ETRANSFER", target = "ETRANSFER"),
            @ValueMapping(source = "WIRES", target = "WIRES"),
            @ValueMapping(source = "VISA", target = "VISA"),
            @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
    })
    PaymentRailType domainToPaymentRailDto(PaymentRail paymentCategoryType);

    /**
     * Enum mapping from internal domain payment rail to payment rail dto
     *
     * @param internalPaymentRail
     * @return
     */
    @ValueMappings({
            @ValueMapping(source = "INTERNAL", target = "INTERNAL"),
            @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
    })
    PaymentRailType internalDomainToPaymentRailDto(InternalPaymentRail internalPaymentRail);

    /**
     * Enum mapping from domain payment category to payment category dto
     *
     * @param paymentCategory
     * @return
     */
    @ValueMappings({
            @ValueMapping(source = "DEBIT_PULL", target = "DEBIT_PULL"),
            @ValueMapping(source = "CREDIT_PUSH", target = "CREDIT_PUSH"),
            @ValueMapping(source = "SEND_PAYMENT", target = "SEND_PAYMENT"),
            @ValueMapping(source = "COMPLETE_PAYMENT", target = "COMPLETE_PAYMENT"),
            @ValueMapping(source = "ALIAS_DEPOSIT_PAYMENT", target = "ALIAS_DEPOSIT_PAYMENT"),
            @ValueMapping(source = "ACCOUNT_NUMBER_DEPOSIT_PAYMENT", target = "ACCOUNT_NUMBER_DEPOSIT_PAYMENT"),
            @ValueMapping(source = "FULFILL_REQUEST_FOR_PAYMENT", target = "FULFILL_REQUEST_FOR_PAYMENT"),
            @ValueMapping(source = "REVERSAL", target = "REVERSAL"),
            @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
    })
    PaymentCategoryType domainToPaymentCategoryDto(PaymentCategory paymentCategory);

    /**
     * Enum mapping from domain internal payment category to payment category dto
     *
     * @param internalPaymentCategory
     * @return
     */
    @ValueMappings({
            @ValueMapping(source = "PREFUND_RESERVE", target = "RESERVE"),
            @ValueMapping(source = "CORRECTION", target = "CORRECTION"),
            @ValueMapping(source = MappingConstants.ANY_UNMAPPED, target = MappingConstants.NULL)
    })
    PaymentCategoryType internalDomainToPaymentCategoryDto(InternalPaymentCategory internalPaymentCategory);

    /**
     * Instruction -- DTO to persistence entity mapper.
     *
     * @param instruction dto instruction object
     * @return
     */
    @Mappings({@Mapping(source = "transactions", target = "transactions"),
            @Mapping(source = "profileRefId", target = "profileRefId", qualifiedByName = "stringToUUID"),
            @Mapping(source = "accountRefId", target = "accountRefId", qualifiedByName = "stringToUUID")})
    InstructionEntity fromInstructionToInstructionEntity(Instruction instruction);


    /**
     * Balance -- persistence entity to DTO mapper.
     *
     * @param balanceEntity persistence entity balance object
     * @return
     */
    @Mapping(source = "effectiveToDateTime", target = "effectiveToDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "effectiveFromDateTime", target = "effectiveFromDateTime", qualifiedByName = "dateTimeToOffSet")
    List<Balance> fromBalanceEntityListToBalanceList(List<BalanceEntity> balanceEntity);


    @Mapping(source = "effectiveToDateTime", target = "effectiveToDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "effectiveFromDateTime", target = "effectiveFromDateTime", qualifiedByName = "dateTimeToOffSet")
    Balance fromBalanceEntityToBalance(BalanceEntity balanceEntity);

    @Mapping(source = "effectiveToDateTime", target = "effectiveToDateTime", qualifiedByName = "offsetToLocalDateTime")
    @Mapping(source = "effectiveFromDateTime", target = "effectiveFromDateTime", qualifiedByName = "offsetToLocalDateTime")
    List<BalanceEntity> fromBalanceListToBalanceEntityList(List<Balance> balanceList);


    @Mapping(source = "effectiveToDateTime", target = "effectiveToDateTime", qualifiedByName = "offsetToLocalDateTime")
    @Mapping(source = "effectiveFromDateTime", target = "effectiveFromDateTime", qualifiedByName = "offsetToLocalDateTime")
    BalanceEntity fromBalanceToBalanceEntity(Balance balance);


    /**
     * Transaction -- DTO to persistence entity mapper.
     *
     * @param transaction dto transaction object
     * @return
     */
    @Mapping(source = "profileRefId", target = "profileRefId", qualifiedByName = "stringToUUID")
    @Mapping(source = "accountRefId", target = "accountRefId", qualifiedByName = "stringToUUID")
    @Mapping(source = "acceptanceDateTime", target = "acceptanceDateTime", qualifiedByName = "offsetToLocalDateTime")
    @Mapping(source = "dueDateTime", target = "dueDateTime", qualifiedByName = "offsetToLocalDateTime")
    @Mapping(source = "effectiveDateTime", target = "effectiveDateTime", qualifiedByName = "offsetToLocalDateTime")
    @Mapping(source = "finalizationDateTime", target = "finalizationDateTime", qualifiedByName = "offsetToLocalDateTime")
    @Mapping(target = "amount", expression = "java(MapperUtil.negativeValue(transaction))")
    TransactionEntity fromTransactionToTransactionEntity(Transaction transaction);


     /**
     * Instruction -- persistence entity to DTO mapper.
     *
     * @param instructionEntity persistence entity instruction object
     * @return
     */
    @Mappings({@Mapping(source = "transactions", target = "transactions"),
            @Mapping(source = "profileRefId", target = "profileRefId", qualifiedByName = "uuidToString"),
            @Mapping(source = "accountRefId", target = "accountRefId", qualifiedByName = "uuidToString")})
    Instruction fromInstructionEntityToInstruction(InstructionEntity instructionEntity);


    @Mappings({@Mapping(source = "transactions", target = "transactions"),
            @Mapping(source = "profileRefId", target = "profileRefId", qualifiedByName = "uuidToString"),
            @Mapping(source = "accountRefId", target = "accountRefId", qualifiedByName = "uuidToString")})
    List<Instruction> fromInstructionEntityListToInstructionList(List<InstructionEntity> instructionEntity);

    /**
     * Transaction -- persistence entity to DTO mapper.
     *
     * @param transactionEntity persistence entity transaction object
     * @return
     */
    @Mapping(source = "dueDateTime", target = "dueDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "acceptanceDateTime", target = "acceptanceDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "effectiveDateTime", target = "effectiveDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "finalizationDateTime", target = "finalizationDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "profileRefId", target = "profileRefId", qualifiedByName = "uuidToString")
    @Mapping(source = "accountRefId", target = "accountRefId", qualifiedByName = "uuidToString")
    @Mapping(target = "amount", expression = "java(MapperUtil.positiveValue(transactionEntity))")
    Transaction fromTransactionEntityToTransaction(TransactionEntity transactionEntity);

    /**
     * Instruction -- DTO to domain response mapper.
     *
     * @param instruction instruction dto object
     * @return
     */
    @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "updatedDateTime", target = "updatedDateTime", qualifiedByName = "dateTimeToOffSet")
    InitiateLedgerTransactionResponse fromInstructionToInstructionResponse(Instruction instruction);

    @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet")
    BalanceSnapshotResponse fromBalanceToBalanceResponse(Balance balance);

    @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet")
    List<BalanceSnapshotResponse> fromBalanceListToBalanceResponseList(List<Balance> balance);

    /**
     * Instruction -- DTO to domain response mapper.
     *
     * @param instruction instruction DTO object
     * @return
     */
    @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet")
    InitiateLedgerPrefundReserveResponse fromInstructionToPrefundReserveResponse(Instruction instruction);

    /**
     * Transaction -- domain request to DTO mapper.
     *
     * @param initiateLedgerTransactionRequestTransactions request domain object
     * @return
     */
    @Named("fromInitiateLedgerTransactionRequestEntriesToTransactionList")
    List<Transaction> fromInitiateLedgerTransactionRequestEntriesToTransactionList(
            List<InitiateLedgerTransactionRequestTransactionsInner> initiateLedgerTransactionRequestTransactions);

    /**
     * Transaction -- domain request to DTO mapper.
     *
     * @param initiateLedgerPrefundReserveRequestTransactions request domain object
     * @return
     */
    @Named("fromInitiateLedgerPrefundReserveRequestEntriesToTransactionList")
    @Mapping(source = "acceptanceDateTime", target = "acceptanceDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "effectiveDateTime", target = "effectiveDateTime", qualifiedByName = "dateTimeToOffSet")
    List<Transaction> fromInitiateLedgerPrefundReserveRequestEntriesToTransactionList(
            List<InitiateLedgerTransactionRequestTransactionsInner> initiateLedgerPrefundReserveRequestTransactions);

    /**
     * Instruction -- domain request to DTO mapper.
     *
     * @param transactionRequestBody
     * @return
     */
    @Mapping(source = "transactions", target = "transactions")
    Instruction fromTransactionRequestBodyToInstruction(InitiateLedgerTransactionRequest transactionRequestBody);

    /**
     * Instruction -- domain request to DTO mapper.
     *
     * @param initiateLedgerPrefundReserveRequest
     * @return
     */
    @Mapping(source = "transactions", target = "transactions")
    Instruction fromPrefundReserveRequestBodyToInstruction(InitiateLedgerPrefundReserveRequest initiateLedgerPrefundReserveRequest);


    /**
     * Transaction -- DTO to domain response mapper.
     *
     * @param transaction transaction dto object
     * @return
     */
    @Mapping(source = "dueDateTime", target = "dueDateTime", qualifiedByName = "offSetToOffSet")
    @Mapping(source = "acceptanceDateTime", target = "acceptanceDateTime", qualifiedByName = "offSetToOffSet")
    @Mapping(source = "effectiveDateTime", target = "effectiveDateTime", qualifiedByName = "offSetToOffSet")
    RetrieveLedgerTransactionResponse fromTransactionToRetrieveTransactionResponse(Transaction transaction);


    /**
     * Instruction -- DTO to domain response mapper.
     *
     * @param instruction instruction dto object
     * @return
     */
    @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "updatedDateTime", target = "updatedDateTime", qualifiedByName = "dateTimeToOffSet")
    // @Mapping(source = "status", target = "status", qualifiedByName = "setInstructionStatus")
    RetrieveLedgerTransactionsResponse fromInstructionToRetrieveInstructionResponse(Instruction instruction);


    @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "updatedDateTime", target = "updatedDateTime", qualifiedByName = "dateTimeToOffSet")
    List<RetrieveLedgerTransactionsResponse> fromInstructionListToRetrieveInstructionResponseList(List<Instruction> instructionList);

    /**
     * Instruction -- DTO to domain response mapper.
     *
     * @param instruction instruction DTO object
     * @return
     */
    @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet")
    @Mapping(source = "updatedDateTime", target = "updatedDateTime", qualifiedByName = "dateTimeToOffSet")
    CommitLedgerTransactionResponse fromInstructionToCommitTransactionResponse(Instruction instruction);

    /**
     * Mapping for InstructionStatus (DTO to domain), required to handle the INIT_PENDING status which is an intermediary status.
     */
    @ValueMappings({
            @ValueMapping(source = "INIT_PENDING", target = "PENDING"),
            @ValueMapping(source = "ROLLBACKED_SYSTEM", target = "ROLLBACKED")
    })
    InstructionStatus instructionStatusDtoToDomain(com.peoplestrust.transaction.persistence.entity.InstructionStatus instructionStatusDto);

    /**
     * Mapping for InstructionStatus (DTO to domain), required to handle the INIT_PENDING status which is an intermediary status.
     */
    @ValueMappings({
            @ValueMapping(source = "INIT_PENDING", target = "PENDING"),
            @ValueMapping(source = "ROLLBACKED_SYSTEM", target = "ROLLBACKED")
    })
    TransactionStatus transactionStatusDtoToDomain(com.peoplestrust.transaction.persistence.entity.TransactionStatus trasactionStatusDto);

    /**
     * Mapping for PaymentCategory (DTO to Domain)
     */

    /**
     * Utility function to map string to UUID.
     *
     * @param uuid uuid
     * @return
     */
    @Named(value = "stringToUUID")
    default UUID stringToUUID(String uuid) {
        return UUID.fromString(uuid);
    }

    /**
     * Utility function to map UUID to string.
     *
     * @param uuid uuid
     * @return
     */
    @Named(value = "uuidToString")
    default String uuidToString(UUID uuid) {
        Base64.getEncoder().encode(asByteArray(uuid));
        return uuid.toString();
    }

    /**
     * Utility function to map local date time to offset date time
     *
     * @param dt
     * @return
     */
    @Named(value = "dateTimeToOffSet")
    default OffsetDateTime dateTimeToOffSet(LocalDateTime dt) {
        OffsetDateTime result = null;
        if (dt != null) {
            result = OffsetDateTime.of(dt, ZoneOffset.UTC);
        }

        return result;
    }


    /**
     * Utility function to map offset date time to UTC time zone
     *
     * @param dt
     * @return
     */
    @Named(value = "offSetToOffSet")
    default OffsetDateTime offSetToOffSet(OffsetDateTime dt) {
        if (dt != null) {
            return dt.atZoneSameInstant(ZoneId.of(APICommonUtilConstant.UTC)).toOffsetDateTime();
        } else {
            return null;
        }
    }


    /**
     * Utility function to map offset date time to local date time
     *
     * @param offsetDateTime
     * @return
     */
    @Named("offsetToLocalDateTime")
    default LocalDateTime offsetToLocalDateTime(OffsetDateTime offsetDateTime) {
        if (offsetDateTime == null) {
            return null;
        }
        return offsetDateTime.toLocalDateTime();
    }

}

