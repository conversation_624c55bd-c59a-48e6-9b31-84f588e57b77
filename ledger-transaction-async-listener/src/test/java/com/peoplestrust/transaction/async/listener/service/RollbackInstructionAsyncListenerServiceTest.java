package com.peoplestrust.transaction.async.listener.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.async.listener.config.AsyncTransactionListenerProperty;
import com.peoplestrust.transaction.async.listener.config.KafkaListenerConfig;
import com.peoplestrust.transaction.async.listener.mapper.AsyncTransactionListenerMapper;
import com.peoplestrust.transaction.domain.model.InstructionStatus;
import com.peoplestrust.transaction.domain.model.LedgerInstruction;
import com.peoplestrust.transaction.domain.model.LedgerTransaction;
import com.peoplestrust.transaction.domain.model.PaymentCategory;
import com.peoplestrust.transaction.domain.model.PaymentRail;
import com.peoplestrust.transaction.domain.model.TransactionFlow;
import com.peoplestrust.transaction.domain.model.TransactionStatus;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.utility.RandomString;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class RollbackInstructionAsyncListenerServiceTest {

  @InjectMocks
  private AsyncTransactionListenerServiceImpl asyncTransactionService;

  @Mock
  AsyncTransactionListenerProperty asyncTransactionProperty;

  @Mock
  private KafkaTemplate kafkaTemplate;

  @Mock
  RestTemplate restTemplate;

  @Mock
  AsyncTransactionListenerMapper asyncTransactionListenerMapper;

  @Autowired
  KafkaListenerConfig kafkaListenerConfig;

  @Mock
  AsyncTransactionListenerProperty asyncTransactionListenerProperty;

  @Mock
  ObjectMapper objectMapper;

  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();

  @Test
  public void commitInstructionTest() throws Exception {
    LedgerInstruction instruction = buildInstructionData();
    ResponseEntity<HttpStatus> rollbackLedgerTransactionResponse = new ResponseEntity<HttpStatus>(HttpStatus.NO_CONTENT);
    String topic = "pending_transaction";
    String key = "ROLL|" + instruction.getInstructionRefId();
    ConsumerRecord<String, LedgerInstruction> record = new ConsumerRecord<>(topic, 0, 123L, key, instruction);
    RecordHeaders headers = new RecordHeaders();
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.getBytes());
    headers.add(APICommonUtilConstant.HEADER_OPERATION_ID, APICommonUtilConstant.COMMIT.getBytes());
    headers.add(APICommonUtilConstant.HEADER_INSTRUCTION_REF_ID, instruction.getInstructionRefId().getBytes());

    when(restTemplate.exchange((String) any(), (HttpMethod) any(), (HttpEntity<?>) any(), (Class<HttpStatus>) any())).thenReturn(
        rollbackLedgerTransactionResponse);
    asyncTransactionService.rollBackTransaction(headers,key);
  }

  private LedgerInstruction buildInstructionData() {
    String word = "TEST_INST" + RandomString.make(7);
    List<LedgerTransaction> transactions = buildTransactionData();
    LedgerInstruction instruction = new LedgerInstruction();
    instruction.setInstructionRefId(word);
    instruction.setPaymentRail(PaymentRail.ETRANSFER);
    instruction.setCreatedDateTime(DateUtils.offsetDateTime());
    instruction.setStatus(InstructionStatus.POSTED);
    instruction.setTransactions(transactions);
    return instruction;
  }

  private List<LedgerTransaction> buildTransactionData() {
    List<LedgerTransaction> list = new ArrayList<>();

    LedgerTransaction t1 = new LedgerTransaction();
    t1.setTransactionRefId(UUID.randomUUID().toString());
    t1.setPaymentCategory(PaymentCategory.COMPLETE_PAYMENT);
    t1.setTransactionFlow(TransactionFlow.CREDIT);
    t1.setMonetaryUnit("CAD");
    t1.setStatus(TransactionStatus.POSTED);
    t1.setAmount(new BigDecimal(1000));
    t1.setAcceptanceDateTime(DateUtils.offsetDateTime());
    t1.setEffectiveDateTime(DateUtils.offsetDateTime());

    LedgerTransaction t2 = new LedgerTransaction();
    t2.setTransactionRefId(UUID.randomUUID().toString());
    t2.setPaymentCategory(PaymentCategory.SEND_PAYMENT);
    t2.setTransactionFlow(TransactionFlow.DEBIT);
    t2.setMonetaryUnit("CAD");
    t2.setStatus(TransactionStatus.POSTED);
    t2.setAmount(new BigDecimal(100));
    t2.setAcceptanceDateTime(DateUtils.offsetDateTime());
    t2.setEffectiveDateTime(DateUtils.offsetDateTime());

    list.add(t1);
    list.add(t2);
    return list;
  }
}
