import subprocess
import sys
import json
import yaml
import glob
from pathlib import Path
import docker
import re

allChangeK8sFile = []

class yamlFile:
    yamlFile = ""
    dockerImageLink = ""
    valuesYaml = any
    kind = ""


def scanAll_K8sFile(deploy_env):
    yamlfiles = []

    # use the glob file filter we only pickup batch + listener yaml file to process
    for file in glob.glob("environments/k8s_" + deploy_env + "/k8s-*-deployment.yaml"):
        try:
            with open(file, 'r') as f:
                oneYamlFile = yamlFile()
                oneYamlFile.valuesYaml = yaml.load(f, Loader=yaml.FullLoader)
                if oneYamlFile.valuesYaml['kind'] == "Deployment":
                    oneYamlFile.kind = "Deployment"
                    oneYamlFile.yamlFile = file
                    oneYamlFile.dockerImageLink = oneYamlFile.valuesYaml['spec']['template']['spec']['containers'][0][
                        'image']
                    yamlfiles.append(oneYamlFile)
        except (FileNotFoundError, IOError, PermissionError) as e:
            print(f"Error reading file {file}: {str(e)}")
            raise e
        except yaml.YAMLError as e:
            print(f"Error in YAML file {file}: {str(e)}")
            raise e

    print("total match yaml file count:" + str(len(yamlfiles)))
    return yamlfiles


def matchK8sFile(all_k8sFiles, ecrFile, timeStamp):
    matchFiles = []
    try:
        for oneYamlFile in all_k8sFiles:
            dockerImageLinks = oneYamlFile.dockerImageLink.split("/")
            if len(dockerImageLinks) > 1:
                dockerImageCompare = dockerImageLinks[1].split(":")
                if len(dockerImageCompare) == 2 and dockerImageCompare[0] == ecrFile:
                    imageParts = oneYamlFile.dockerImageLink.split(":")
                    updateDockerImageLink = imageParts[0] + ":" + timeStamp

                    # Read the content of the file
                    yaml_content = Path(oneYamlFile.yamlFile).read_text()
                    # print("Original content of file:", oneYamlFile.yamlFile)
                    # print(yaml_content)

                    # Replace the image link
                    yaml_content = yaml_content.replace(f"image: {oneYamlFile.dockerImageLink}",
                                                        f"image: {updateDockerImageLink}")

                    # Debugging info for DataDog tag change
                    old_datadog_tag = re.findall(r'tags\.datadoghq\.com/version: ".*?"', yaml_content) + re.findall(
                        r"tags\.datadoghq\.com/version: '.*?'", yaml_content)
                    if old_datadog_tag:
                        print("Old DataDog tag(s):", old_datadog_tag)

                        # Replace all occurrences of the Datadog version tag using regex
                        yaml_content = re.sub(r'tags\.datadoghq\.com/version: ".*?"',
                                              f'tags.datadoghq.com/version: "{timeStamp}"', yaml_content)
                        yaml_content = re.sub(r"tags\.datadoghq\.com/version: '.*?'",
                                              f"tags.datadoghq.com/version: '{timeStamp}'", yaml_content)

                        new_datadog_tag = re.findall(r'tags\.datadoghq\.com/version: ".*?"', yaml_content) + re.findall(
                            r"tags\.datadoghq\.com/version: '.*?'", yaml_content)
                        print("New DataDog tag(s):", new_datadog_tag)
                    else:
                        print("No DataDog tag found in file:", oneYamlFile.yamlFile)

                    # print("Updated content of file:", oneYamlFile.yamlFile)
                    # print(yaml_content)

                    # Write the updated content back to the file
                    Path(oneYamlFile.yamlFile).write_text(yaml_content)

                    matchFiles.append(oneYamlFile.yamlFile)
                    allChangeK8sFile.append(oneYamlFile.yamlFile)
                    break
    except Exception as e:
        print(f"Error occurred: {str(e)}")
        raise e

    return len(matchFiles)


def buildProject(project, timeStamp, all_k8sFiles):
    shortImageShaValue = findDockerImageSha256(project["ecr"])
    #     print("Image Sha256 value:"+str(shortImageShaValue))
    project["imageSha"] = shortImageShaValue

    #     print("inside build project for project:"+project["ecr"])
    # scan find the k8s yaml file and update it
    project["yamlFileCount"] = matchK8sFile(all_k8sFiles, project["ecr"], timeStamp)


def findDockerImageSha256(imageName):
    cli = docker.from_env()
    dockerImages = cli.images.list()
    sha256ShortImageId = None
    for dockerImage in dockerImages:
        imageTags = dockerImage.tags
        if len(imageTags) > 1:
            imageTag0 = imageTags[1].split(":")[0]
            if imageTag0 == imageName:
                sha256ShortImageId = dockerImage.id.replace("sha256:", "")[0:12]
                break

    if sha256ShortImageId:
        print(imageName + " sha256:" + sha256ShortImageId)
        return sha256ShortImageId
    else:
        print(f"No SHA256 ID found for {imageName}")
        return None


def findDockerImageTag(imageName):
    cli = docker.from_env()
    dockerImages = cli.images.list()
    imageTag = None
    for dockerImage in dockerImages:
        imageTags = dockerImage.tags
        if len(imageTags) > 1:
            imageTag0 = imageTags[1].split(":")[0]
            if imageTag0 == imageName:
                imageTag = imageTags[1].split(":")[1]
                print(imageName + " Tag:" + imageTag)
                break

    if imageTag:
        return imageTag
    else:
        print(f"No tag found for {imageName}")
        return None


def loadConfigJson(configFile):
    # Opening JSON config file
    f = open(configFile)

    # returns JSON object as a dictionary
    projectJsonData = json.load(f)

    return projectJsonData


def autoK8sApply(apply):
    # section for auto k8s apply
    print("")
    for k8sFile in allChangeK8sFile:
        # change the file path format to be match macos
        k8sApplyCmd = "kubectl apply -f " + k8sFile.replace("\\", "/")
        print("{:<100}".format(k8sApplyCmd))
        if apply:
            subprocess.call(k8sApplyCmd, shell=True)


def is_docker_running():
    try:
        subprocess.check_output(['docker', 'version'])
        return True
    except subprocess.CalledProcessError:
        return False


if __name__ == "__main__":

    if not is_docker_running():
        print("Docker is not running!")
        sys.exit(1)

    # load json config file
    projectJsonData = loadConfigJson(sys.argv[1])

    # scan disk find all match files
    all_k8sFiles = scanAll_K8sFile(sys.argv[2])

    for project in projectJsonData['projects']:
        # find timestamp from util build result
        timeStamp = findDockerImageTag(project["ecr"])
        if not timeStamp:
            print(f"Skipping project {project['ecr']} due to missing timestamp.")
            continue
        buildProject(project, timeStamp, all_k8sFiles)

    print("")
    print("Final Build Result")
    for project in projectJsonData['projects']:
        print("{:<40} {:<15} {:<5} {:<100}".format(project["folder"], project["imageSha"], project["yamlFileCount"],
                                                   project["imageUrl"]))

    # https://stackoverflow.com/questions/31684375/automatically-create-requirements-txt
    if (len(sys.argv) == 4 and sys.argv[3] == "applyk8s"):
        autoK8sApply(True)
    else:
        autoK8sApply(False)
