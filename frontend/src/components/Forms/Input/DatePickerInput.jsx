import React, { useState, useEffect } from 'react'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { LocalizationProvider } from '@mui/x-date-pickers'
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker'
import moment from 'moment'
import * as _ from 'lodash'
import * as S from './styled'

const DatePickerInput = (props) => {
    const [value, setValue] = useState('')

    const handleChange = (newValue) => {
        if (!newValue) {
            setValue('')
            props.setValues({
                ...props.values,
                [props?.name]: ''
            })

            return false;
        }

        const date = moment(newValue).format('YYYY-MM-DD:HH:mm')
        setValue(moment(date).toDate())

      if(typeof props.onChange === 'function') {

        props.setValues({
            ...props.values,
            [props?.name]: date
        })
      }
    }

    useEffect(() => {
        if (props.value) {
            setValue(moment(props.value).toDate())
        }
    }, [props.value])

    const maxDateProps = props.maxDate ? {
        maxDate: props.maxDate
    } : {}

    return (
        <LocalizationProvider dateAdapter={AdapterMoment}>
             <DateTimePicker
                name={props.name}
                value={value}
                onChange={handleChange}
                {...maxDateProps}
                renderInput={(params) => {
                    const newParams = {
                        ...params,
                        helperText: props?.helperText,
                        error: props?.error
                    }
                    return <S.Input variant='outlined' data-testid='date-input' inputProps={{
                        ...params.inputProps,
                        placeholder: props?.placeholder,

                    }} {..._.omit(newParams, ['inputProps'])} />
                }}
            />
        </LocalizationProvider>

    )
}



export default DatePickerInput