import { useCallback } from "react";
import CreateAccount from "./CreateAccount";
import useCreateAccount from "hooks/Account/useCreateAccount";
import useUpdateAccount from "hooks/Account/useUpdateAccount";
import CreateAccountForm from "constants/forms/createAccount";
import UpdateACcountForm from "constants/forms/updateAccount";
import ModalProvider from "contexts/ModalProvider";
import schema, { updateSchema } from "./schema";

const CreateAccountContainer = (props) => {
  const mutation = useCreateAccount();
  const updateMutation = useUpdateAccount(props?.parentId, props?.data?.ref_Id);

  const handleSubmit = useCallback(
    (values) => {
      if (props.data && props.edit) {
        updateMutation.mutate({
          ...values,
          profile_id: props?.parentId,
          id: props?.data?.ref_Id,
        });
      } else {
        mutation.mutate({
          ...values,
          profile_id: props?.parentId,
        });
      }
    },
    [props.data]
  );

  return (
    <ModalProvider
      data={props.data}
      edit={props.edit}
      schema={props.data && props.edit ? updateSchema : schema}
      handleClose={props.handleClose}
      fields={props.data && props.edit ? UpdateACcountForm : CreateAccountForm}
      show={props.show}
      handleSubmit={handleSubmit}
    >
      <CreateAccount
        {...props}
        mutationData={
          props.data && props.edit ? updateMutation.data : mutation.data
        }
        data-testid="modal-component"
      />
    </ModalProvider>
  );
};

export default CreateAccountContainer;
