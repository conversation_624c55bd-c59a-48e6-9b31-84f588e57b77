import React from 'react'
import Select from 'components/Forms/Select/Select'
import { render } from '@testing-library/react'
import findByTestId from 'utils/findByTestId'


const setUp = (props) => render(
    <Select {...props} />
)


describe('Select component testing', () => {
    it ('Should render component without props', async () => {
        setUp({})
        const component = await findByTestId('custom-select')

        expect(component).toBeInTheDocument()
    })

    it ('Should render with options', async () => {
        const options = [
            {
                name: 'name',
                value: 'name'
            },
            {
                name: 'name1',
                value: 'name1'
            }
        ]

        setUp({
            options
        })

        const selectOptions = await findByTestId('select-option', 'all')

        expect(selectOptions.length).toEqual(options.length + 1)
    })
})