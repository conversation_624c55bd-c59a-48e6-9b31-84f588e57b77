import { useQuery } from "react-query";
import baseQueryOptions from "constants/baseQueryOptions";
import { searchTransaction } from "services/transaction";

const useSearchTransaction = (props) => {
  const { data, isLoading, refetch } = useQuery(
    ["searchTransaction", props],
    () => searchTransaction(props),
    {
      ...baseQueryOptions,
    }
  );

  return { data, isLoading, refetch };
};

export default useSearchTransaction;
