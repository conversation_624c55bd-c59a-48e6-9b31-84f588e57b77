import React, { createContext, useContext, useEffect, useState } from 'react'
import { useFormik } from 'formik'
import _ from 'lodash'

const ModalContext = createContext()

export const useModal = () => useContext(ModalContext)
    
const resetForm = (formik) => {
    const newObj = {}
    if (!formik?.values) return

    for (let key in formik.values) {
        newObj[key] = ''
    }

    formik.resetForm({
        values: newObj
    })

}

const ModalProvider = (props) => {
    const { data, schema, handleSubmit, handleClose, edit, fields } = props

    const [show, setShow] = useState(false)

    const onFormikSubmit = (values, data) => {
        handleSubmit(values, data)
    }

    const dataByFields = data && fields?.reduce((acc, current) => {
        return _.set(acc, current.name, _.get(data, current.name))
    }, {}) || {}

    const formik = useFormik({
        initialValues: dataByFields || {},
        validationSchema: schema,
        onSubmit: onFormikSubmit,
        enableReinitialize: true
    })

    useEffect(() => {
        setShow(props.show)
    }, [props.show])

    useEffect(() => {
        if (!edit) {
            resetForm(formik)
        }
    }, [edit])

    const closeModal = () => {
        setShow(false)
        
        if (typeof handleClose === 'function') {
            handleClose()
        }

        if(!edit) {
            resetForm()
        }
    }

    return (
        <ModalContext.Provider value={{
            show,
            setShow,
            closeModal,
            formik
        }}>
            {props.children}
        </ModalContext.Provider>
    )
}


export default ModalProvider