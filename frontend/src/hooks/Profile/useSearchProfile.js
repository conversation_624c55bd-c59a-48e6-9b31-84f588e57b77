import useLazyQuery from 'hooks/useLazyQuery'
import baseQueryOptions from 'constants/baseQueryOptions'
import { searchProfilesService } from 'services/profile'

const useSearchProfiles = (props) => {
    const [trigger, { data }] = useLazyQuery(['searchProfiles', props], () => searchProfilesService(props), {
        keepPreviousData: true,
        ...baseQueryOptions
    })

    return { trigger, data }

}

export default useSearchProfiles