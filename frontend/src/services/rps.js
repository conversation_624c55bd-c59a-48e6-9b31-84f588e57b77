import api from './client/axiosInstance'
import moment from 'moment'

export const getRps = async ({ start_time, end_time }) => {
    const start = moment.utc(new Date(start_time), 'MM/DD/YYYY h:mm').local().format("YYYY-MM-DDTHH:mm:ss")
    const end = moment.utc(new Date(end_time), 'MM/DD/YYYY h:mm').local().format("YYYY-MM-DDTHH:mm:ss")
    const response = await api().get(`health/cloudwatch/request/?start_time=${start}&end_time=${end}`)

    const data = response?.data || []

    return data
}