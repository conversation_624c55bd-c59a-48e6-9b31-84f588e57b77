@startuml

skinparam componentStyle rectangle

package "S3 CSV Monitor Service" {

    component "main.py" as Main
    note right of Main
      - Polls S3 for CSV files
      - Reads and processes CSV
      - Moves files to archive/failed
      - Validates records in DB
      - Emits metrics
      - Starts health check server
    end note

    component "metrics.py" as Metrics
    note right of Metrics
      - Initializes Datadog StatsD
      - Emits counters, gauges, timings
      - Adds env/service/version tags
    end note

    component "Flask Health Server" as Flask
    note right of Flask
      - Serves /actuator/health endpoint
      - Suppresses log noise for health
    end note

    database "PostgreSQL" as PostgreSQL
    component "AWS S3" as S3
    note right of S3
      - input/
      - archive/
      - failed/
    end note
}

package "External Integrations" {

    [Datadog Agent] as Datadog
    component "Environment Variables (.env)" as Env
    note right of Env
      - AWS creds, S3 paths
      - DB credentials
      - DD_DOGSTATSD_URL
      - ENV, SERVICE, VERSION
    end note
}

Main --> S3 : list_objects_v2()\nget_object()\ncopy/delete_object()
Main --> PostgreSQL : connect()\nSELECT * FROM transactions
Main --> Flask : start_health_server()
Main --> Metrics : increment()\ngauge()\ntiming()\nevent()
Main --> Env : os.getenv()\nload_dotenv()

Metrics --> Datadog : statsd.* (via Unix socket)
Metrics --> Env : os.getenv() for\nENV, SERVICE, VERSION

Flask --> Flask : /actuator/health (200 OK)

@enduml
