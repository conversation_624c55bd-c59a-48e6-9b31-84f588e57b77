import React from 'react'
import Button from 'components/Button'
import TableFilter from './TableFilter'
import * as S from './styled'

const TableHeader = (props) => {
    const CreateModal = props.createModal || null

    return (
        <S.TableHeader className='table-header' data-testid='table-header'>
            {
                props?.heading && (
                    <h3>{ props.heading }</h3>
                )
            }
            <div className='table-actions'>
                {
                    props.filters && (
                        <S.Filter className='w-100 table-filters' data-testid='table-filters'>
                            <TableFilter 
                                filterForms={props.filterForms}
                                filters={props.filters}
                                onFilter={props?.onFilter}
                                withOnChange={props.withOnChange}
                                buttonDisabled={props.filterButtonDisabled}
                            />
                        </S.Filter>
                    )
                }

                {
                    CreateModal && (
                        <Button type='primary' onClick={(e) => props.onCreateClick(e, {}, false)} testId='create-modal-button'>
                            {
                                props.createText || 'Create'
                            }
                        </Button>
                    )
                }
            </div>
            {
                props.description && (
                    <p>
                        {props.description}
                    </p>
                )
            }
            
        </S.TableHeader >
    )
}

export default TableHeader