package com.peoplestrust.account.api.v1.service;

import com.peoplestrust.account.api.v1.AccountApplication;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.MonetaryUnit;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ContextConfiguration(classes = AccountApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class GetAccountServiceTestIT {

    private static String profileId = UUID.randomUUID().toString();
    @Autowired
    AccountService accountService;
    @MockBean
    ReadAccountRepository readAccountRepository;
    private Account account;


    private AccountEntity getAccountData(Account account) {
        AccountEntity accountEntity = new AccountEntity();
        OptionsEntity optionsEntity = new OptionsEntity();
        optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
        optionsEntity.setOverdraftAmount(account.getOptions().getOverdraftAmount());
        accountEntity.setCreatedDateTime(DateUtils.offset());
        accountEntity.setUpdatedDateTime(DateUtils.offset());
        accountEntity.setName(account.getName());
        accountEntity.setOptions(optionsEntity);
        accountEntity.setRefId(UUID.fromString(account.getRefId()));
        accountEntity.setDescription(account.getDescription());
        MonetaryUnit monetaryUnit = MonetaryUnit.valueOf(account.getMonetaryUnit());
        accountEntity.setMonetaryUnit(monetaryUnit);
        accountEntity.setStatus(AccountStatus.INACTIVE);
        accountEntity.setProfileId(UUID.fromString(profileId));
        return accountEntity;
    }

    @Test
    public void getAccountByIdTest() throws Exception {
        Account account = TestUtil.createAccount(profileId);

        String profileIdAndAccountId = profileId + "_" + account.getRefId();
        AccountEntity accountEntity = new AccountEntity();
        accountEntity.setProfileId(UUID.fromString(profileId));
        accountEntity.setRefId(UUID.fromString(account.getRefId()));
        accountEntity.setStatus(AccountStatus.ACTIVE);

        OptionsEntity optionsEntity = new OptionsEntity();
        optionsEntity.setOverdraftAmount(new BigDecimal(100.00));
        optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
        accountEntity.setOptions(optionsEntity);

        when(readAccountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));
        Account getResponse = accountService.getAccountById(account.getRefId(), profileId, profileIdAndAccountId);
        assertNotNull(getResponse);
        assertEquals(account.getRefId(), getResponse.getRefId());
        assertEquals(account.getOptions().getFundHoldDays(), getResponse.getOptions().getFundHoldDays());
    }
}
