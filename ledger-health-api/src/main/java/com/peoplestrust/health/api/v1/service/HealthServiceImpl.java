package com.peoplestrust.health.api.v1.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.health.api.v1.config.HealthProperty;
import com.peoplestrust.health.domain.model.BalanceSnapshotStatus;
import com.peoplestrust.health.domain.model.HealthResponse;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.exception.ExceptionUtil;
import com.peoplestrust.util.api.common.exception.InvalidFieldException;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import com.peoplestrust.util.api.common.exception.ValidationException;
import com.peoplestrust.util.api.common.util.Messages;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

/**
 * Health service.
 */
@Slf4j
@Service
@Component
public class HealthServiceImpl implements HealthService {

  /**
   * Balance persistence repository
   */
  @Autowired
  ReadBalanceRepository readBalanceRepository;

  @Autowired
  HealthProperty healthProperty;

  @Autowired
  private CloudWatchLogQuery cloudWatchLogQuery;

  private ObjectMapper objectMapper;

  /**
   * {@inheritDoc}
   */
  @PerfLogger
  @Override
  public HealthResponse validateSnapshots() throws Exception {

    HealthResponse healthResponse = null;
    try {
      LocalDateTime now = LocalDateTime.now();
      LocalDateTime yesterday = now.minusDays(1);

      // Calculate start of yesterday
      LocalDateTime startOfYesterday = yesterday.withHour(0).withMinute(0).withSecond(0)
          .withNano(0);

      // Calculate end of yesterday with the current hour and minute
      LocalDateTime endOfYesterday = yesterday.withHour(now.getHour()).withMinute(now.getMinute())
          .withSecond(0).withNano(0);

      // Calculate start of today
      LocalDateTime startOfToday = now.withHour(0).withMinute(0).withSecond(0).withNano(0);

      // Fetching snapshots count for yesterday
      Long yesterdaySnapshots = readBalanceRepository.countSnapshotsByDateTime(startOfYesterday,
          endOfYesterday);

      log.debug("validateSnapshots yesterday:{} total:{} start:{} end:{}",
          yesterday, yesterdaySnapshots, startOfYesterday, endOfYesterday);

      // Fetching snapshots count for today
      Long todaySnapshots = readBalanceRepository.countSnapshotsByDateTime(startOfToday, now);

      log.debug("validateSnapshots today:{} total:{} start:{} end:{}",
          now, todaySnapshots, startOfToday, now);

      boolean result = todaySnapshots >= yesterdaySnapshots;

      healthResponse = new HealthResponse();
      if (result) {
        healthResponse.setStatus(BalanceSnapshotStatus.SUCCESS);
      } else {
        healthResponse.setStatus(BalanceSnapshotStatus.FAILED);
      }

      log.debug("validateSnapshots yesterday:{} total:{} today:{} total:{} result:{}",
          yesterday, yesterdaySnapshots, now, todaySnapshots, result);

    } catch (DataIntegrityViolationException ex) {
      log.warn("data integrity violation on validate balance Snapshots", ex);
      throw new InvalidFieldException(ex.getCause().getCause().getMessage());
    } catch (IllegalArgumentException ex) {
      log.warn("illegal argument exception on validate balance Snapshots", ex);
      throw new InvalidFieldException(ExceptionUtil.message(ex));
    } catch (org.springframework.web.client.HttpClientErrorException ex) {
      throw new com.peoplestrust.util.api.common.exception.HttpClientErrorException(
          ex.getMessage());
    } catch (ResourceAccessException ex) {
      throw new ResourceNotFoundException(ex.getCause().getMessage());
    } catch (Exception ex) {
      log.error("unexpected exception on validate balance Snapshots", ex);
      throw new Exception(ex.getMessage());
    }
    return healthResponse;
  }

  @PerfLogger
  @Override
  public String getTop10RpsWithDateRange(LocalDateTime startTime, LocalDateTime endTime)
      throws Exception {
    if (endTime.isAfter(startTime)) {

      // Specify the time zone for US-East-1 region
      ZoneId zoneId = ZoneId.of(APICommonUtilConstant.US_EAST_TIMEZONE);

      // Convert LocalDateTime to Instantly using the specified time zone, then to epoch milli
      long startEpochMilli = startTime.atZone(zoneId).toInstant().toEpochMilli();
      long endEpochMilli = endTime.atZone(zoneId).toInstant().toEpochMilli();

      String namespace = healthProperty.getEksNameSpace();
      String query = "fields @timestamp, @log " +
          "| filter kubernetes.labels.app=\"transaction-v1\" " +
          "| filter kubernetes.namespace_name=\"" + namespace + "\" " +
          "| parse log \"* * * [*] [*]-[*] * - *\" as pg_date, pg_time, pg_log_level, pg_sa_id, pg_interaction_id, pg_thread_id, pg_class, pg_message "
          +
          "| filter pg_class = 'APIPayloadLogger' " +
          "| filter pg_message like '| REQUEST |' " +
          "| parse pg_message \"* | REQUEST | method=*, url=*, headers=[*], body=*\" as pg_req_api, pg_req_method, pg_req_url, pg_req_headers, pg_req_body "
          +
          "| filter pg_req_method = 'POST' OR pg_req_method = 'PATCH' " +
          "| stats count(*) as all, sum(pg_req_method like 'POST') as initiate, sum(pg_req_method like 'PATCH') as commit by bin(1s) "
          +
          "| sort by all desc " +
          "| limit 10";
      log.info("getTop10RpsWithDateRange query :{} startTime:{} endTime:{}", query, startEpochMilli,
          endEpochMilli);
      String data = cloudWatchLogQuery.executeQuery(healthProperty.getCloudWatchLogGroup(),
          startEpochMilli,
          endEpochMilli,
          query);

      return data;
    } else {
      throw new ValidationException(Messages.END_TIME_LESS_THAN_START_TIME);
    }

  }

  @PerfLogger
  @Override
  public String getTop10ResponseWithDateRange(LocalDateTime startTime, LocalDateTime endTime)
      throws Exception {
    if (endTime.isAfter(startTime)) {
      // Specify the time zone for US-East-1 region
      ZoneId zoneId = ZoneId.of(APICommonUtilConstant.US_EAST_TIMEZONE);

      // Convert LocalDateTime to Instantly using the specified time zone, then to epoch milli
      long startEpochMilli = startTime.atZone(zoneId).toInstant().toEpochMilli();
      long endEpochMilli = endTime.atZone(zoneId).toInstant().toEpochMilli();

      String namespace = healthProperty.getEksNameSpace();
      String query = "fields @timestamp, log " +
          "| filter kubernetes.labels.app=\"transaction-v1\" " +
          "| filter kubernetes.namespace_name=\"" + namespace + "\" " +
          "| parse log \"* * * [*] [*]-[*] * - *\" as pg_date, pg_time, pg_log_level, pg_sa_id, pg_interaction_id, pg_thread_id, pg_class, pg_message "
          +
          "| filter pg_class=\"PerfLogger\" " +
          "| parse pg_message \"* | * | * | * ms\" as perf_method, perf_start_date, perf_end_date, perf_elapsed "
          +
          "| filter perf_method like 'TransactionController' " +
          "| stats average(perf_elapsed) as average_response_time, min(perf_elapsed) as minimum_response_time, max(perf_elapsed) as maximum_response_time by bin(60s) "
          +
          "| sort maximum_response_time desc " +
          "| limit 10";
      log.info("getTop10ResponseWithDateRange query :{} startTime:{} endTime:{}", query,
          startEpochMilli, endEpochMilli);
      String data = cloudWatchLogQuery.executeQuery(healthProperty.getCloudWatchLogGroup(),
          startEpochMilli,
          endEpochMilli,
          query);

      return data;
    } else {
      throw new ValidationException(Messages.END_TIME_LESS_THAN_START_TIME);
    }

  }

  @PerfLogger
  @Override
  public String getTimeOutErrorWithDateRange(LocalDateTime startTime, LocalDateTime endTime)
      throws Exception {
    if (endTime.isAfter(startTime)) {
      // Specify the time zone for US-East-1 region
      ZoneId zoneId = ZoneId.of(APICommonUtilConstant.US_EAST_TIMEZONE);

      // Convert LocalDateTime to Instantly using the specified time zone, then to epoch milli
      long startEpochMilli = startTime.atZone(zoneId).toInstant().toEpochMilli();
      long endEpochMilli = endTime.atZone(zoneId).toInstant().toEpochMilli();

      String namespace = healthProperty.getEksNameSpace();
      String totalTimeoutQuery =
          "fields @timestamp, @message\n"
              + "| filter kubernetes.labels.app=\"transaction-v1\" OR kubernetes.labels.app=\"transaction-async-v1\"| filter @message like 'timed'\n"
              + "| filter kubernetes.namespace_name=\"" + namespace + "\""
              + "| stats count() as totalTimeout";
      String errorDetailQuery =
          "fields @timestamp, @message\n"
              + "| filter kubernetes.labels.app=\"transaction-v1\" OR kubernetes.labels.app=\"transaction-async-v1\"| filter @message like 'timed'\n"
              + "| filter kubernetes.namespace_name=\"" + namespace + "\""
              + "| stats count() as timeout_count by bin(1s), kubernetes.labels.app as method, log as error_message, @timestamp | sort @timestamp desc"
              + "|limit 10";
      log.info(
          "totalInternalErrorCountQuery: {} startTime:{} endTime:{}",
          totalTimeoutQuery,
          startEpochMilli,
          endEpochMilli);
      String totalTimeout =
          cloudWatchLogQuery.executeQuery(
              healthProperty.getCloudWatchLogGroup(),
              startEpochMilli,
              endEpochMilli,
              totalTimeoutQuery);
      log.info("totalTimeout from Service: {}", totalTimeout);
      JSONArray totalTimeoutResponse = new JSONArray(totalTimeout);
      if (!totalTimeoutResponse.isEmpty()) {
        log.info(
            "errorDetailQuery: {} startTime:{} endTime:{}",
            errorDetailQuery,
            startEpochMilli,
            endEpochMilli);
        String errorDetail =
            cloudWatchLogQuery.executeQuery(
                healthProperty.getCloudWatchLogGroup(),
                startEpochMilli,
                endEpochMilli,
                errorDetailQuery);
        Map<String, Object> linkedHashMap = new LinkedHashMap<>();
        JSONArray totalTimeoutDetail = new JSONArray(errorDetail);
        objectMapper = new ObjectMapper();
        JsonNode node =
            objectMapper.readTree(Objects.requireNonNull(totalTimeoutResponse).toString());
        linkedHashMap.put("totalTimeout", node.get(0).get("totalTimeout").asText());
        linkedHashMap.put("error_details", totalTimeoutDetail);
        JSONObject responseObject = new JSONObject(linkedHashMap);
        return responseObject.toString();
      }
      return totalTimeoutResponse.toString();
    } else {
      throw new ValidationException(Messages.END_TIME_LESS_THAN_START_TIME);
    }

  }

  @PerfLogger
  @Override
  public String getHttpInternalErrorWithDateRange(LocalDateTime startTime, LocalDateTime endTime)
      throws Exception {
    if (endTime.isAfter(startTime)) {

      ZoneId zoneId = ZoneId.of(APICommonUtilConstant.US_EAST_TIMEZONE);
      // Convert LocalDateTime to Instantly using the specified time zone, then to epoch milli
      long startEpochMilli = startTime.atZone(zoneId).toInstant().toEpochMilli();
      long endEpochMilli = endTime.atZone(zoneId).toInstant().toEpochMilli();

      String namespace = healthProperty.getEksNameSpace();

      String totalInternalErrorCountQuery = "fields @timestamp, log\n" +
          "| filter kubernetes.labels.app=\"transaction-v1\" OR kubernetes.labels.app=\"transaction-async-v1\"\n" +
          "| filter kubernetes.namespace_name=\"" + namespace + "\"" +
          "| filter log like 'httpStatus=503' or  log like 'httpStatus=500'" +
          "| stats count() as totalInternal_ErrorCount";
      String errorDetailQuery =
          "fields @timestamp, log, @logstream\n"
              + "| filter kubernetes.labels.app=\"transaction-v1\" OR kubernetes.labels.app=\"transaction-async-v1\"\n"
              + "| filter kubernetes.namespace_name=\"" + namespace + "\""
              + "| filter log like 'httpStatus=503' or  log like 'httpStatus=500'\n"
              + "| parse log \"* * * [*] [*]-[*] * - *\" as pg_date, pg_time, pg_log_level, pg_sa_id, pg_interaction_id, pg_thread_id, pg_class, pg_message\n"
              + "| parse pg_message \"* | * | *, *, *\" as perf_method, response, httpStatus, header, body\n"
              + "| parse body 'body={\"error\":*' as path | parse path '\"code\":\"*\"' as code| parse path '\"additional_information\":\"*\"' as additional_information\n"
              + "| stats count() as error_count by bin(1s), @timestamp, perf_method as method, httpStatus as error_code, code as error_message, additional_information| sort @timestamp desc \n"
              + "| limit 10";
      log.info("totalInternalErrorCountQuery: {} startTime:{} endTime:{}", totalInternalErrorCountQuery,
          startEpochMilli, endEpochMilli);
      String totalInternalErrorCount = cloudWatchLogQuery.executeQuery(healthProperty.getCloudWatchLogGroup(),
          startEpochMilli,
          endEpochMilli,
          totalInternalErrorCountQuery);
      JSONArray internalErrorCountResponse = new JSONArray(totalInternalErrorCount);
      if (!internalErrorCountResponse.isEmpty()) {
        log.info("errorDetailQuery: {} startTime:{} endTime:{}", errorDetailQuery,
            startEpochMilli, endEpochMilli);
        String errorDetail =
            cloudWatchLogQuery.executeQuery(
                healthProperty.getCloudWatchLogGroup(),
                startEpochMilli,
                endEpochMilli,
                errorDetailQuery);
        Map<String, Object> linkedHashMap = new LinkedHashMap<>();
        JSONArray internalErrorCountDetail = new JSONArray(errorDetail);
        objectMapper = new ObjectMapper();
        JsonNode node = objectMapper.readTree(Objects.requireNonNull(internalErrorCountResponse).toString());
        linkedHashMap.put("totalInternal_ErrorCount", node.get(0).get("totalInternal_ErrorCount").asText());
        linkedHashMap.put("error_details", internalErrorCountDetail);
        JSONObject jsonObject = new JSONObject(linkedHashMap);
        return jsonObject.toString();
      }
      return internalErrorCountResponse.toString();

    } else {
      throw new ValidationException(Messages.END_TIME_LESS_THAN_START_TIME);
    }
  }
}
