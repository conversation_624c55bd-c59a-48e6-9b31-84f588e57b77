import TransactionDetails from './TransactionDetails'
import ModalProvider from 'contexts/ModalProvider'

const TransactionDetailsContainer = (props) => {

    if (!props.data.profile_id || !props.data.account_ref_id || !props.data.instruction_ref_id || !props.data.transaction_ref_id) {
        return null
    }

    const params = { 
        profileId: props.data.profile_id,
        accountId: props.data.account_ref_id,
        instructionId: props.data.instruction_ref_id,
        transactionId: props.data.transaction_ref_id,
        paymentRail: props.data.paymentRail
    }

    return (
        <ModalProvider
            handleClose={props.handleClose}
            show={props.show}
        >
            <TransactionDetails {...props} params={params} />
        </ModalProvider>
    )
}

export default TransactionDetailsContainer
