import os
from datadog import initialize, statsd

# Extract environment info with sensible defaults
ENV = os.getenv("ENV", "staging")
DOMAIN = os.getenv("DOMAIN", "general-ledger")
SERVICE = os.getenv("SERVICE", "gl-recon-etransfer-batch-v1")
VERSION = os.getenv("VERSION", "latest")

def init_datadog():
  """
  Initialize the Datadog statsd client using a Unix domain socket
  and emit a service startup event.
  """
  # Remove 'unix://' prefix if present in the socket path
  socket_path = os.getenv("DD_DOGSTATSD_URL", "/var/run/datadog/dsd.socket").removeprefix("unix://")

  initialize(
      statsd_namespace=DOMAIN,
      statsd_socket_path=socket_path,
  )

  # ✅ Replace with a startup metric
  statsd.increment("service.started", tags=get_env_tags())

def get_env_tags(extra_tags=None):
  """
  Return consistent Datadog environment tags, optionally appending more.
  """
  base_tags = [
    f"env:{ENV}",
    f"service:{SERVICE}",
    f"version:{VERSION}",
  ]
  return base_tags + (extra_tags or [])

def increment(metric_name, tags=None):
  """
  Increment a Datadog counter with environment tags.
  """
  statsd.increment(metric_name, tags=get_env_tags(tags))

def gauge(metric_name, value, tags=None):
  """
  Send a gauge metric with environment tags.
  """
  statsd.gauge(metric_name, value, tags=get_env_tags(tags))

def timing(metric_name, value, tags=None):
  """
  Send a timing metric with environment tags.
  """
  statsd.timing(metric_name, value, tags=get_env_tags(tags))

def event(title, text, alert_type="info", tags=None):
  """
  Send a Datadog event with environment tags.
  """
  statsd.event(title, text, alert_type=alert_type, tags=get_env_tags(tags))
