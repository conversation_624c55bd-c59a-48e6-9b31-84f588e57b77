package com.peoplestrust.transaction.scheduler.jobs;

import com.peoplestrust.transaction.scheduler.service.TransactionRollBackScheduledJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RollBackJob {

  /**
   * Service layer
   */
  @Autowired
  TransactionRollBackScheduledJob transactionRollBackScheduledJob;

  @Scheduled(cron = "${scheduler.rollBack.transactions}")
  public void doRollback() {

    transactionRollBackScheduledJob.runTask();
  }

}
