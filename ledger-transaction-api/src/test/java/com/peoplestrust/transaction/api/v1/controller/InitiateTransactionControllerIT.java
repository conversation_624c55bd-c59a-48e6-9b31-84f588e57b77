package com.peoplestrust.transaction.api.v1.controller;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionRequest;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionResponse;
import com.peoplestrust.transaction.domain.model.PaymentCategory;
import com.peoplestrust.transaction.domain.model.PaymentRail;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.JsonUtil;
import com.peoplestrust.util.api.common.util.Messages;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class InitiateTransactionControllerIT {

    private static final String URL_INITIATE_TRANSACTION = "/v1/ledger/transaction";
    private static final String requestId = UUID.randomUUID().toString();
    private static final String interactionId = UUID.randomUUID().toString();
    private static final String profileRefId = UUID.randomUUID().toString();
    private static final String accountRefId = UUID.randomUUID().toString();

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    ValidationService validationService;

    @MockBean
    ReadInstructionRepository readInstructionRepository;

    @MockBean
    ReadTransactionRepository readTransactionRepository;

    private ObjectMapper objectMapper;
    private HttpHeaders headers;

    @BeforeEach
    public void setupBeforeTest() {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
                .enable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID);

        headers = new HttpHeaders();
        headers.add(APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, requestId);
        headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
        headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
        headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
        headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    }

    @Test
    void initiateTransaction_validInput_success() throws Exception {
        InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();

        Account account = TestUtil.createAccount(accountRefId, profileRefId);

        when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
        when(validationService.getAccount(any(), any(), any())).thenReturn(account);

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_INITIATE_TRANSACTION)
                        .headers(headers)
                        .content(JsonUtil.toString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andReturn();

        String jsonResponse = result.getResponse().getContentAsString();
        InitiateLedgerTransactionResponse actualResponse = objectMapper.readValue(jsonResponse, InitiateLedgerTransactionResponse.class);
        Assertions.assertNotNull(actualResponse);
        assertEquals(3, actualResponse.getTransactions().size());
    }

    @Test
    void initiateTransaction_invalidPaymentRail_failure() throws Exception {
        InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();
        request.setPaymentRail(PaymentRail.INTERNAL);

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_INITIATE_TRANSACTION)
                        .headers(headers)
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Test
    void initiateTransaction_invalidPaymentCategory_failure() throws Exception {
        InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();
        request.getTransactions().get(0).setPaymentCategory(PaymentCategory.REVERSAL);

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_INITIATE_TRANSACTION)
                        .headers(headers)
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Test
    void initiateTransaction_missingRequiredHeader_failure() throws Exception {
        InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_INITIATE_TRANSACTION)
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Test
    void initiateTransaction_invalidAcceptanceDateTime_failure() throws Exception {
        InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();
        Account account = TestUtil.createAccount(accountRefId, profileRefId);

        when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
        when(validationService.getAccount(any(), any(), any())).thenReturn(account);
        String invalidRequestJson = objectMapper.writeValueAsString(request);
        invalidRequestJson = invalidRequestJson.replace("\"acceptance_date_time\":\"" + request.getTransactions().get(0).getAcceptanceDateTime() + "\"", "\"acceptance_date_time\":\"2\"");

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_INITIATE_TRANSACTION)
                        .headers(headers)
                        .content(invalidRequestJson)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Disabled
    @Test
    void initiateTransaction_invalidDueDateTime_failure() throws Exception {
        InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();
        Account account = TestUtil.createAccount(accountRefId, profileRefId);

        when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
        when(validationService.getAccount(any(), any(), any())).thenReturn(account);
        String invalidRequestJson = objectMapper.writeValueAsString(request);
        invalidRequestJson = invalidRequestJson.replace("\"due_date_time\":\"" + request.getTransactions().get(0).getDueDateTime() + "\"", "\"due_date_time\":\"2\"");

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_INITIATE_TRANSACTION)
                        .headers(headers)
                        .content(invalidRequestJson)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andReturn();
    }

    @Test
    void initiateTransaction_duplicateTransaction_failure() throws Exception {
        InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();
        Account account = TestUtil.createAccount(accountRefId, profileRefId);

        when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
        when(validationService.getAccount(any(), any(), any())).thenReturn(account);

        // Mock the repository to return an existing transaction for the transaction ID check
        // This simulates finding an existing transaction with the same ID
        TransactionEntity existingTransaction = new TransactionEntity();
        existingTransaction.setProfileRefId(UUID.fromString(profileRefId));
        existingTransaction.setAccountRefId(UUID.fromString(accountRefId));
        when(readTransactionRepository.findByTransactionRefId(any())).thenReturn(existingTransaction);

        MvcResult result = mockMvc.perform(MockMvcRequestBuilders.post(URL_INITIATE_TRANSACTION)
                        .headers(headers)
                        .content(JsonUtil.toString(request))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andReturn();

        // Verify the error response contains the expected error message
        String jsonResponse = result.getResponse().getContentAsString();
        com.fasterxml.jackson.databind.JsonNode jsonNode = objectMapper.readTree(jsonResponse);
        assertEquals(Messages.DUPLICATE_TRANSACTION, jsonNode.get("error").get("additional_information").asText());
    }

}
