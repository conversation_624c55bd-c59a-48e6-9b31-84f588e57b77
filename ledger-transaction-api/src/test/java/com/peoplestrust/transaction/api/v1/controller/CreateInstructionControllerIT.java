package com.peoplestrust.transaction.api.v1.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionRequest;
import com.peoplestrust.transaction.domain.model.InitiateLedgerTransactionResponse;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.UUID;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class CreateInstructionControllerIT {

  private static final String URL = "/v1/ledger/transaction";
  private static final String requestId = UUID.randomUUID().toString();
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  private HttpHeaders headers;
  private ObjectMapper objectMapper;
  private String transactionRefId;
  private String instructionRefId;

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  public void createInstructionTest() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();

    MvcResult result = this.mockMvc.perform(
            MockMvcRequestBuilders.post(URL)
                .headers(headers)
                .content(JsonUtil.toString(request))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andReturn();

    InitiateLedgerTransactionResponse response = JsonUtil.toObject(
        result.getResponse().getContentAsString(), InitiateLedgerTransactionResponse.class);

    transactionRefId = response.getTransactions().get(0).getTransactionRefId();
    instructionRefId = response.getInstructionRefId();

    Assertions.assertNotNull(instructionRefId);
    Assertions.assertNotNull(transactionRefId);
  }

  public String createInstruction() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    InitiateLedgerTransactionRequest request = TestUtil.buildInstruction();

    MvcResult result = this.mockMvc.perform(
            MockMvcRequestBuilders.post(URL)
                .headers(headers)
                .content(JsonUtil.toString(request))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andReturn();

    InitiateLedgerTransactionResponse response = JsonUtil.toObject(
        result.getResponse().getContentAsString(), InitiateLedgerTransactionResponse.class);

    transactionRefId = response.getTransactions().get(0).getTransactionRefId();
    return response.getInstructionRefId();
  }
}
