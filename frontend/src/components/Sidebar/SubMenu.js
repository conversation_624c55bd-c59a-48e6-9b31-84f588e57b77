import SubMenuItem from './SubMenuItem'
import * as S from './styled'

const Submenu = ({ data, setOpen, currentPath }) => {

    if (data) {
        return (
            <S.Submenu data-testid='submenu'>
                {
                    data.map(submenu => (
                        <SubMenuItem
                            testId='submenu-item'
                            route={submenu}
                            key={submenu.to}
                            setOpen={setOpen}
                            currentPath={currentPath}
                        />
                    ))
                }
            </S.Submenu>
        )
    }

    return null
}

export default Submenu