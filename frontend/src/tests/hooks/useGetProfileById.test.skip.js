// useGetProfileById.test.skip.js
import { renderHook, waitFor } from '@testing-library/react-hooks'
import useGetProfileById from 'hooks/Profile/useGetProfileById'
import profiles from 'data/profiles'
import { fetch_profile_by_id } from 'mocks/mswHandlers/profilesHandler'
import { QueryClientProvider } from 'react-query'
import { mswServer } from 'mocks/mswServer'
import queryClient from 'services/client/queryClient'

describe('Should get profiles by Id', () => {
    it('Should return current profile data correctly', async () => {
        // 1. Use the MSW handler for fetching a profile by id
        mswServer.use(fetch_profile_by_id)

        // 2. Wrap our hook in QueryClientProvider so React Query can run
        const wrapper = ({ children }) => (
            <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
        )

        // 3. Render our hook
        const { result } = renderHook(() => useGetProfileById(1), { wrapper })

        // 4. Wait until the hook returns the expected data
        await waitFor(() => {
            expect(result.current).toEqual(profiles.find((d) => d.id === 1))
        })
    })
})
