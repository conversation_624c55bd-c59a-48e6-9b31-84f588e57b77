package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.transaction.domain.model.RetrieveBalanceResponse;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class RetrieveBalanceServiceTest {
  @Mock
  private ReadTransactionRepository readTransactionRepository;
  @InjectMocks
  private TransactionServiceImpl transactionService;
  @Mock
  private InstructionRepository instructionRepository;
  @Mock
  private ReadBalanceRepository readBalanceRepository;

  String accountId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();


  @Test
  public void retrieveBalanceTest() throws Exception {

    BigDecimal fundHoldAmount = BigDecimal.valueOf(1000);
    BigDecimal credit = BigDecimal.valueOf(500);
    BigDecimal debit = BigDecimal.valueOf(300);
    BalanceEntity balanceEntity = getBalanceEntity();
    when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(),any())).thenReturn(Optional.of(balanceEntity));
    when(readTransactionRepository.getFundHoldAmount(any(), any(), any(), any())).thenReturn(Optional.of(fundHoldAmount));
    when(readTransactionRepository.sumAmountByDateRange(any(), any(), any(), any())).thenReturn(Optional.of(credit));
    when(readTransactionRepository.sumAmountByDateRange(any(), any(), any(), any())).thenReturn(Optional.of(debit));
    RetrieveBalanceResponse amountVal = transactionService.retrieveAccountBalance(accountId, profileId, UUID.randomUUID().toString(), "100");
    assertNotNull(amountVal);
  }


  private BalanceEntity getBalanceEntity() {
    BalanceEntity balanceEntity = new BalanceEntity();
    balanceEntity.setTotalAmount(BigDecimal.valueOf(500));
    balanceEntity.setTotalReserveAmount(BigDecimal.valueOf(50));
    return balanceEntity;
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileId), UUID.fromString(accountId)).stream().
        forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }
}
