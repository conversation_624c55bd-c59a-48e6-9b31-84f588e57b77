package com.peoplestrust.account.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.domain.model.CreateLedgerAccountRequest;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.JsonUtil;
import java.time.Instant;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class CreateAccountControllerTest {

  private static final String URL = "/v1/ledger/account";
  @Autowired
  private MockMvc mockMvc;
  private CreateLedgerAccountRequest request;
  private HttpHeaders headers;
  private ObjectMapper objectMapper;

  @BeforeEach
  public void setupBeforeTest() throws Exception {
    request = TestUtil.createAccountPostRequest();
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    UUID interactionId = UUID.randomUUID();

    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.AUTHORIZATION,
        "eyJraWQiOiJxeXZnRXIzV0VFT2hONlR1N2hoakg5Q3BkcXp3WGJJY0l1VFdRSCtVRWRnPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O-vUHqyUAwObCijDZx2CGUjye7czV63cYqS1tL_t_sNbVmLS2ghapd75hwAZbDnZ8h8pN1S9JkPkx5qF3r7OticHO0UAyfLUennNN1g713cYTD_pIVqXXd7WUowyvstLhaVpNwhpqTID5vuu8f3J_yHImjUBUtYGvnR4uV7WnpWEVL2LsbhCa0iB_LeDBZEws7OYBvPwqOCaf7y58EMukOUtwizMEAslEofF-5K9RXG_Q8TKApcKixnheMqDZ7IcbKlK9XLMqn-sr147BvvaPRH1DmQbrPxFSz1z6Ihw_wAI21qDKXWX-OxGv_-gYq9NF-a-HpO0qHo_GB4NDh31EA");
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, "28dfaa4d-5889-4cbd-bd46-e6201e560b6c");
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);
  }

  @Test
  void postAccountNull_Name() throws Exception {
    request.setName(null);
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountNull_Description() throws Exception {
    request.setDescription(null);
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountNull_MonetaryUnit() throws Exception {
    request.setMonetaryUnit(null);
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountEmpty_Name() throws Exception {
    request.setName("");
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountEmpty_Description() throws Exception {
    request.setDescription("");
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountEmpty_MonetaryUnit() throws Exception {
    request.setMonetaryUnit("");
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountWhiteSpace_Name() throws Exception {
    request.setName("  ");
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountWhiteSpace_Description() throws Exception {
    request.setDescription("  ");
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountWhiteSpace_MonetaryUnit() throws Exception {
    request.setMonetaryUnit("  ");
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccountNull_Name2() throws Exception {
    request.setName(null);

    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  void postAccount_MissingHeader() throws Exception {
    request = TestUtil.createAccountPostRequest();
    headers.remove(APICommonUtilConstant.AUTHORIZATION);
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest())
        .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("MISSING_HEADER", jsonNode.get("error").get("code").asText());
    assertEquals("Authorization", jsonNode.get("error").get("additional_information").asText());
  }

  @Test
  void postAccount_BadToken() throws Exception {
    request = TestUtil.createAccountPostRequest();
    headers.set(APICommonUtilConstant.AUTHORIZATION, TestUtil.BAD_JWT_TOKEN);
    MvcResult result = this.mockMvc.perform(MockMvcRequestBuilders.post(URL)
            .headers(headers)
            .content(JsonUtil.toString(request))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized())
        .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode = objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("code").asText());
    assertEquals("UNAUTHORIZED", jsonNode.get("error").get("additional_information").asText());
  }
}
