apiVersion: apps/v1
kind: Deployment
metadata:
  name: health-deployment
  namespace: pg-ledger-qa
  labels:
    app: health-v1
    domain: general-ledger
    tags.datadoghq.com/env: qa
    tags.datadoghq.com/service: gl-health-v1
    tags.datadoghq.com/version: "20250606134639"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: health-v1
  template:
    metadata:
      annotations:
        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: health-v1
        admission.datadoghq.com/enabled: "false"
        domain: general-ledger
        tags.datadoghq.com/env: qa
        tags.datadoghq.com/service: gl-health-v1
        tags.datadoghq.com/version: "20250606134639"
    spec:
      containers:
        - name: health-container
          image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/ledger-health:20250606134639
          resources:
            requests:
              memory: "384Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "400m"
          ports:
            - containerPort: 8080
          # Startup probe has higher priority over the two other probe types. Until the Startup Probe succeeds, all the other Probes are disabled.
          startupProbe:
             httpGet:
               path: /actuator/health
               port: 8080
               scheme: HTTP
             initialDelaySeconds: 10
             timeoutSeconds: 2
             periodSeconds: 15
             successThreshold: 1
             failureThreshold: 20
          # Kubelet uses liveness probes to know when to restart a container. If the liveness probe fails, the kubelet kills the container, and the container is subjected to its restart policy.
          livenessProbe:
             httpGet:
               path: /actuator/health
               port: 8080
               scheme: HTTP
             initialDelaySeconds: 20
             timeoutSeconds: 30
             periodSeconds: 300
             successThreshold: 1
             failureThreshold: 3
          # Kubernetes makes sure the readiness probe passes before allowing a service to send traffic to the pod. Unlike a liveness probe, a readiness probe doesn’t kill the container. If the readiness probe fails, Kubernetes simply hides the container’s Pod from corresponding Services, so that no traffic is redirected to it.
          readinessProbe:
             httpGet:
               path: /actuator/health
               port: 8080
               scheme: HTTP
             initialDelaySeconds: 20
             timeoutSeconds: 2
             periodSeconds: 5
             successThreshold: 1
             failureThreshold: 3
          env:
            - name: TZ
              value: 'America/Toronto'
            - name: DISABLE_COGNITO_JWT_VERIFICATION
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.jwt.token.check.disable
            - name: SPRING_PROFILES_ACTIVE
              value: 'cloud'
            - name: TRANSACTION_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.host
            - name: TRANSACTION_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.schema
            - name: TRANSACTION_DB_PORT
              value: '5432'
            - name: TRANSACTION_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.name
            - name: TRANSACTION_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.username
            - name: TRANSACTION_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.password
            - name: READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.read.host
            - name: READ_DB_PORT
              value: '5432'
            - name: READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.name
            - name: READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.username
            - name: READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.password
            - name: MAXIMUM_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.maximum.pool.size
            - name: DB_CONNECTION_TIME_OUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.connection.timeout
            - name: EKS_NAME_SPACE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.health.eks-name-space
            - name: CLOUD_WATCH_LOG_GROUP
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.health.cloud-watch-log-group
            - name: ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-secret
                  key: aws.access.key
            - name: SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-secret
                  key: aws.secret.key
            - name: LOG_LEVEL_ROOT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.root
            - name: LOG_LEVEL_COM_PEOPLESTRUST
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.com.peoplestrust
            - name: LOG_LEVEL_HIBERNATE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.hibernate
            - name: LOG_LEVEL_API_PAYLOAD_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.api.payload
            - name: LOG_LEVEL_PERF_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.perf
            - name: LOG_LEVEL_FLOW_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.flow