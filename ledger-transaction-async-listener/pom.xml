<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>ledger-parent-api</artifactId>
    <groupId>com.peoplestrust</groupId>
    <version>1.0-SNAPSHOT</version>
    <relativePath>../ledger-parent-api/pom.xml</relativePath>
  </parent>
  <artifactId>ledger-transaction-async-listener</artifactId>
  <name>Ledger::Transaction::Async::Listener</name>
  <dependencies>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>common-api</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>ledger-transaction-domain</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.peoplestrust</groupId>
      <artifactId>ledger-transaction-async-domain</artifactId>
      <version>1.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents.client5</groupId>
      <artifactId>httpclient5</artifactId>
      <version>5.2.1</version>
    </dependency>
  </dependencies>
</project>