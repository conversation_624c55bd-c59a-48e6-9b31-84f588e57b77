import generateDate from "utils/generateDate"

const columns = [
    {
      headerName: '#',
      field: 'id',
      maxWidth: 50,
      flex: 1,
      renderCell:(index) => index.api.getRowIndex(index.row.id) + 1
    },
    {
      headerName: 'Date & Time',
      field: 'bin(1s)',
      flex: 1,
      valueGetter: ({ row }) => generateDate(row['bin(1s)'], 'YYYY-MMMM-DD-HH:mm:ss')
    },
    {
        headerName: 'Error count',
        field: 'error_count',
        flex: 1,
    },
    {
        headerName: 'Error Message',
        field: 'error_message',
        flex: 1,
    },
    {
        headerName: 'Method',
        field: 'method',
        flex: 1,
    }
]

export default columns