apiVersion: apps/v1
kind: Deployment
metadata:
  name: profile-deployment
  namespace: pg-ledger-dev
  labels:
    app: profile-v1
    domain: general-ledger
    tags.datadoghq.com/env: dev
    tags.datadoghq.com/service: gl-profile-v1
    tags.datadoghq.com/version: "20241112161444"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: profile-v1
  template:
    metadata:
      annotations:
        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: profile-v1
        admission.datadoghq.com/enabled: "false"
        domain: general-ledger
        tags.datadoghq.com/env: dev
        tags.datadoghq.com/service: gl-profile-v1
        tags.datadoghq.com/version: "20241112161444"
    spec:
      containers:
        - name: profile-container
          image: 799455639446.dkr.ecr.ca-central-1.amazonaws.com/ledger-profile:20241112161444
          resources:
            requests:
              memory: "384Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "400m"
          ports:
            - containerPort: 8080
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 30 # after (initialDelaySeconds + periodSeconds * failureThreshold) attempts, restart the pod
          # The kubelet uses readiness probes to know when a container is ready to start accepting traffic. A Pod is considered ready when all of its containers are ready. One use of this signal is to control which Pods are used as backends for Services. When a Pod is not ready, it is removed from Service load balancers.
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 3 # after (initialDelaySeconds + periodSeconds * failureThreshold) attempts, restart the pod
          # The kubelet uses liveness probes to know when to restart a container. For example, liveness probes could catch a deadlock, where an application is running, but unable to make progress. Restarting a container in such a state can help to make the application more available despite bugs.
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 600
            periodSeconds: 300
            timeoutSeconds: 30
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: TZ
              value: 'America/Toronto'
            - name: DISABLE_COGNITO_JWT_VERIFICATION
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.jwt.token.check.disable
            - name: SPRING_PROFILES_ACTIVE
              value: 'cloud'
            - name: PROFILE_WRITE_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.host
            - name: PROFILE_READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.read.host
            - name: PROFILE_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.schema
            - name: PROFILE_WRITE_DB_PORT
              value: '5432'
            - name: PROFILE_WRITE_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.name
            - name: PROFILE_WRITE_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.username
            - name: PROFILE_WRITE_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.password
            - name: PROFILE_READ_DB_PORT
              value: '5432'
            - name: PROFILE_READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.name
            - name: PROFILE_READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.username
            - name: PROFILE_READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: profile-database
                  key: sys.database.password
            - name:  REDIS_DB_CLUSTER
              valueFrom:
                secretKeyRef:
                  name: redis-cache
                  key: sys.database.cluster
            - name: REDIS_READ_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.redis.read.timeout
            - name: REDIS_CONNECTION_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.redis.connection.timeout
            - name: MAXIMUM_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.maximum.pool.size
            - name: DB_CONNECTION_TIME_OUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.connection.timeout
            - name: LOG_LEVEL_ROOT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.root
            - name: LOG_LEVEL_COM_PEOPLESTRUST
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.com.peoplestrust
            - name: LOG_LEVEL_HIBERNATE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.hibernate
            - name: LOG_LEVEL_API_PAYLOAD_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.api.payload
            - name: LOG_LEVEL_PERF_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.perf
            - name: LOG_LEVEL_FLOW_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.flow