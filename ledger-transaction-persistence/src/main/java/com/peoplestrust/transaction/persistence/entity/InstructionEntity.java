package com.peoplestrust.transaction.persistence.entity;

import java.util.List;
import java.util.UUID;

import io.hypersistence.utils.hibernate.type.basic.PostgreSQLEnumType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Data
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Table(name = "instructions")
public class InstructionEntity extends DomainEntityTimeStamps {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id", unique = true, nullable = false)
  private Integer id;

  @NotNull
  @Column(name = "profile_ref_id", columnDefinition = "uuid")
  private UUID profileRefId;

  @NotNull
  @Column(name = "account_ref_id",columnDefinition = "uuid")
  private UUID accountRefId;

  @Column(name = "instruction_ref_id")
  private String instructionRefId;

  @Enumerated(EnumType.STRING)
  @Column(name = "payment_rail")
  private PaymentRailType paymentRail;

  @Enumerated(EnumType.STRING)
  @Column(name = "status",columnDefinition = "instructions.instruction_status_type")
  @Type(PostgreSQLEnumType.class)
  private InstructionStatus status;

  @OneToMany(mappedBy = "instruction", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
  private List<TransactionEntity> transactions;
}
