
import { Typography } from '@mui/material'
import { Link } from 'react-router-dom'
import useBreadcrumbs from 'use-react-router-breadcrumbs'
import getRoutes from 'navigation/routes'
import * as S from './styled'

const blackList = ['/profiles']
const healthRoutes = ['/rps', '/response']

const Breadcrumb = (props) => {
    const routes = props.routes || getRoutes(false)

    const breadcrumbs = useBreadcrumbs(routes)

    if (breadcrumbs.length === 1) {
        return null
    }

    return (
        <S.Breadcrumb data-testid='breadcrumb'>
            {
                breadcrumbs.map((crumb, index) => {
                    const last = index === breadcrumbs.length - 1
                    if (blackList.includes(crumb.key)) return null

                    if (healthRoutes.includes(window.location.pathname) && crumb.key === '/') {
                        return (
                            <Typography key={crumb.key} data-testid='breadcrumb-item'>
                                Health
                            </Typography>
                        )
                    }

                    return last ? (
                        <Typography key={crumb.key} data-testid='breadcrumb-item'>
                            {crumb?.match?.route?.name}
                        </Typography>
                    ) : (
                        <Link key={crumb.key} to={crumb?.match?.pathname} data-testid='breadcrumb-item'>
                            {crumb?.match?.route?.name}
                        </Link>
                    )
                })
            }
        </S.Breadcrumb>
    )
}

export default Breadcrumb