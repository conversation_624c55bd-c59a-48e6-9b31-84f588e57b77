import { useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBars } from '@fortawesome/free-solid-svg-icons'
import useAuth from 'hooks/useAuth'
import useGetBalanceSnapshotHealth from 'hooks/Account/useGetBalanceSnapshotHealth'
import useGetTimeoutHealth from 'hooks/Account/useGetTimeoutHealth'
import useGetInternalHealth from 'hooks/Account/useGetInternalHealth'
import Button from 'components/Button'
import * as S from './styled'

const Header = () => {
    const { login, logout, user, isSignedIn, userSessionUpdate } = useAuth()
    const health = useGetBalanceSnapshotHealth(isSignedIn)
    const timeoutHealth = useGetTimeoutHealth(isSignedIn)
    const internalHealth = useGetInternalHealth(isSignedIn)

    useEffect(() => {
      userSessionUpdate()
    }, [])

    return (
        <S.Header data-testid='header'>
            <p data-testid='burger'>
                <FontAwesomeIcon icon={faBars} />
                Admin UI
            </p>

            <S.BalanceHealths>
                {
                    isSignedIn && (
                        <S.BalanceHealth health={internalHealth}>
                            Internal Health
                        </S.BalanceHealth>
                    )
                }
                {
                    isSignedIn && (
                        <S.BalanceHealth health={timeoutHealth}>
                            Timeout Health
                        </S.BalanceHealth>
                    )
                }
                {
                    isSignedIn && (
                        <S.SnapshotHealth health={health?.status}>
                            Balance Snapshot Health
                        </S.SnapshotHealth>
                    )
                }
            </S.BalanceHealths>
            {
                !isSignedIn && (
                    <Button testId='login' onClick={login}>
                        Login
                    </Button>
                )
            }
            {
                isSignedIn && (
                    <Button testId='logout' onClick={logout}>
                        Logout
                    </Button>
                )
            }
        </S.Header>
    )
}

export default Header