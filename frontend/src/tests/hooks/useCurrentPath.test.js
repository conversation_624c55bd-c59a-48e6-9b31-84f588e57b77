// useCurrentPath.test.js
import { renderHook } from '@testing-library/react-hooks';
import useCurrentPath from 'hooks/useCurrentPath';
import { MemoryRouter } from 'react-router-dom';

describe('Find current path based on location', () => {
    it('Should return the route for "/"', () => {
        // Force a route array that definitely has "/" at index 0:
        const routes = [
            { path: '/', name: '<PERSON>Route' },
            { path: '/profiles', name: 'ProfilesRoute' },
        ];

        const wrapper = ({ children }) => (
            <MemoryRouter initialEntries={['/']}>{children}</MemoryRouter>
        );
        const { result } = renderHook(() => useCurrentPath(routes), { wrapper });

        // Now "routes[0]" matches "/"
        expect(result.current).toBe(routes[0]);
    });

    it('Should return the route for "/profiles"', () => {
        // Same forced array, so "/profiles" is at index 1:
        const routes = [
            { path: '/', name: '<PERSON>Rout<PERSON>' },
            { path: '/profiles', name: 'ProfilesRoute' },
        ];

        const wrapper = ({ children }) => (
            <MemoryRouter initialEntries={['/profiles']}>{children}</MemoryRouter>
        );
        const { result } = renderHook(() => useCurrentPath(routes), { wrapper });

        // "routes[1]" matches "/profiles"
        expect(result.current).toBe(routes[1]);
    });
});
