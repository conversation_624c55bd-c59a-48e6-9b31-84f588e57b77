spring:
  datasource:
    url: jdbc:postgresql://${ACCOUNT_READ_DB_HOST}:${ACCOUNT_READ_DB_PORT}/${ACCOUNT_READ_DB_NAME}?currentSchema=${ACCOUNT_DB_SCHEMA}
    username: ${ACCOUNT_READ_DB_USERNAME}
    password: ${ACCOUNT_READ_DB_PASSWORD}
  main:
    allow-bean-definition-overriding: true
  jackson:
    default-property-inclusion: non_empty
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: true
  jpa:
    properties:
      hibernate:
        jdbc.lob.non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
        order_inserts: true
        order_updates: true
        generate_statistics: false
server:
  tomcat:
    mbeanregistry:
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: info,health,metrics
async-transaction:
  api:
    timetolive: 30000
app:
  name: AsyncTransactionAPI

kafka:
  bootstrap-servers: ${KAFKA_SERVER}
  topic: ${TEST_TOPIC_NAME}
  dead:
    letter:
      error:
        topic: ${TEST_ERROR_TOPIC}
      topic: ${TEST_STATUS_FAILED_TOPIC}
  group:
    id: ${CONSUMER_GROUP_ID}
  ssl:
    enabled: ${TEST_SSL_ENABLE}
  sasl:
    jaas:
      config:
        username: ${SASL_USERNAME}
        password: ${SASL_PASSWORD}
  backoff:
    max_failure: ${NO_OF_ATTEMPTS}
    interval: ${BACKOFF_INTERVAL}
  max:
    poll:
      interval:
        idle:
          ms: ${POLL_INTERVAL_IDLE}
        ms: ${POLL_INTERVAL}
      records: ${NO_OF_RECORDS_PER_POLL}
transaction:
  api:
    timeout:
      connection: ${TRANSACTION_API_CONNECTION_TIMEOUT}
      read: ${TRANSACTION_API_READ_TIMEOUT}
    initiate:
      url: ${GL_INITIATE_API_URL}
    commit:
      url: ${GL_COMMIT_API_URL}
    rollback:
      url: ${GL_COMMIT_API_URL}
    reverse:
      url: ${GL_COMMIT_API_URL}