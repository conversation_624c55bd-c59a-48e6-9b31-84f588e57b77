package com.peoplestrust.schedulers.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.MonetaryUnit;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.schedulers.BalanceSnapshotSchedulerApplication;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@SpringBootTest
@ContextConfiguration(classes = {BalanceSnapshotSchedulerApplication.class})
@ExtendWith(SpringExtension.class)
@Slf4j
public class BalanceSnapshotServiceTest {

  String profileRefId = UUID.randomUUID().toString();
  String accountRefId = UUID.randomUUID().toString();

  @MockBean
  ReadTransactionRepository readTransactionRepository;
  @Autowired
  BalanceRepository balanceRepository;
  @Autowired
  ReadBalanceRepository readBalanceRepository;
  @MockBean
  ReadAccountRepository readAccountRepository;
  @Autowired
  BalanceSnapshotService balanceSnapshotService;


  @Test
  public void balanceSnapshotTest() throws Exception {

    List<AccountEntity> accounts = getAccounts();

    when(readTransactionRepository.getFundHoldAmount(any(), any(), any(), any(), any())).thenReturn(Optional.of(BigDecimal.valueOf(1020)));
    when(readTransactionRepository.sumAmountByDateRangeBS((UUID) any(), (UUID) any(), any(), any())).thenReturn(Optional.of(new BigDecimal(600)));
    when(readAccountRepository.findAll()).thenReturn(accounts);
    List<BalanceEntity> balanceEntities = balanceSnapshotService.generateTransactionReBalanceSnapshot();
    assertEquals(balanceEntities.size(), accounts.size());
  }

  private List<AccountEntity> getAccounts() {
    List<AccountEntity> accounts = new ArrayList<>();
    AccountEntity account = new AccountEntity();
    account.setMonetaryUnit(MonetaryUnit.valueOf("CAD"));
    account.setProfileId(UUID.fromString(profileRefId));
    account.setRefId(UUID.fromString(accountRefId));
    accounts.add(account);
    return accounts;
  }


  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    balanceRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileRefId), UUID.fromString(accountRefId)).stream().
        forEach(e -> balanceRepository.delete(e));
    log.trace("clean up - end");
  }
}