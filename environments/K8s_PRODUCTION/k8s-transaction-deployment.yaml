apiVersion: apps/v1
kind: Deployment
metadata:
  name: transaction-deployment
  namespace: pg-ledger-prod
  labels:
    app: transaction-v1
    domain: general-ledger
    tags.datadoghq.com/env: prod
    tags.datadoghq.com/service: gl-transaction-v1
    tags.datadoghq.com/version: "20250819083519"
spec:
  replicas: 13
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  minReadySeconds: 45
  selector:
    matchLabels:
      app: transaction-v1
  template:
    metadata:
      annotations:
        admission.datadoghq.com/java-lib.version: latest
      labels:
        app: transaction-v1
        admission.datadoghq.com/enabled: "true"
        domain: general-ledger
        tags.datadoghq.com/env: prod
        tags.datadoghq.com/service: gl-transaction-v1
        tags.datadoghq.com/version: "20250819083519"
    spec:
      terminationGracePeriodSeconds: 90
      containers:
        - name: transaction-container
          image: 131803872836.dkr.ecr.ca-central-1.amazonaws.com/ledger-transaction:20250819083519
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 45"]
          resources:
            requests:
              memory: "384Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "200m"
          ports:
            - containerPort: 8080
          # Startup probe has higher priority over the two other probe types. Until the Startup Probe succeeds, all the other Probes are disabled.
          startupProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 60
            timeoutSeconds: 2
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 30
          # Kubelet uses liveness probes to know when to restart a container. If the liveness probe fails, the kubelet kills the container, and the container is subjected to its restart policy.
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 600
            timeoutSeconds: 30
            periodSeconds: 300
            successThreshold: 1
            failureThreshold: 3
          # Kubernetes makes sure the readiness probe passes before allowing a service to send traffic to the pod. Unlike a liveness probe, a readiness probe doesn’t kill the container. If the readiness probe fails, Kubernetes simply hides the container’s Pod from corresponding Services, so that no traffic is redirected to it.
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 2
            periodSeconds: 3
            successThreshold: 1
            failureThreshold: 3
          env:
            - name: TZ
              value: "America/Toronto"
            - name: DISABLE_COGNITO_JWT_VERIFICATION
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.jwt.token.check.disable
            - name: SPRING_PROFILES_ACTIVE
              value: "cloud"
            - name: READ_WRITE_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.host
            - name: TRANSACTION_DB_SCHEMA
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.schema
            - name: READ_WRITE_DB_PORT
              value: "5432"
            - name: READ_WRITE_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.name
            - name: READ_WRITE_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.username
            - name: READ_WRITE_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.password

            - name: READ_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.read.host
            - name: READ_DB_PORT
              value: "5432"
            - name: READ_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.name
            - name: READ_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.username
            - name: READ_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: transaction-database
                  key: sys.database.password
            - name: PROFILE_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.profile.url
            - name: ACCOUNT_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.account.url
            - name: PROFILE_ACCOUNT_VALIDATE_API_URL
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: sys.account.validate.url
            - name: USE_STORE_PROCEDURE_FOR_SUM
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.use.store.procedure.for.sum
            - name: TRANSACTION_VALIDATION_SERVICE_CONNECTION_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.transaction.validation-service.timeout.connection
            - name: TRANSACTION_VALIDATION_SERVICE_READ_TIMEOUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.transaction.validation-service.timeout.read
            - name: MAXIMUM_POOL_SIZE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.maximum.pool.size
            - name: DB_CONNECTION_TIME_OUT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.db.connection.timeout
            - name: LOG_LEVEL_ROOT
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.root
            - name: LOG_LEVEL_COM_PEOPLESTRUST
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.com.peoplestrust
            - name: LOG_LEVEL_HIBERNATE
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.hibernate
            - name: LOG_LEVEL_API_PAYLOAD_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.api.payload
            - name: LOG_LEVEL_PERF_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.perf
            - name: LOG_LEVEL_FLOW_LOGGER
              valueFrom:
                configMapKeyRef:
                  name: application-dependencies
                  key: app.log.level.flow
