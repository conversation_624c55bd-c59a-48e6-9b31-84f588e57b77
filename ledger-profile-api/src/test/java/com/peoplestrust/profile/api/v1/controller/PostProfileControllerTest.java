package com.peoplestrust.profile.api.v1.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.peoplestrust.profile.api.v1.ProfileTestUtil;
import com.peoplestrust.profile.domain.model.CreateLedgerProfileRequest;
import com.peoplestrust.profile.domain.model.CreateLedgerProfileResponse;
import com.peoplestrust.profile.persistence.repository.write.ProfileRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;

import java.time.Instant;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class PostProfileControllerTest {

    private static final String URL = "/v1/ledger/profile";
    private static final String requestId = UUID.randomUUID().toString();
    private static final String interactionId = UUID.randomUUID().toString();

    @Autowired
    private MockMvc mockMvc;

    private ObjectMapper objectMapper;
    private HttpHeaders headers;
    private CreateLedgerProfileRequest request;
    private CreateLedgerProfileRequest requestSPC;

    @MockBean
    private ProfileRepository profileRepository;

    @BeforeEach
    public void setupBeforeTest() {
        objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
                .enable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID);

        headers = new HttpHeaders();
        headers.add(APICommonUtilConstant.AUTHORIZATION, ProfileTestUtil.JWT_TOKEN);
        headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
        headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
        headers.add(APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, requestId);

        request = ProfileTestUtil.createProfilePostRequest();
        requestSPC = ProfileTestUtil.createSPCProfilePostRequest();
    }

    @Test
    void postProfile_nullCrmId_failure() throws Exception {
        request.setCrmId(null);
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("crmId"));
    }

    @Test
    void postProfile_nullDisplayName_failure() throws Exception {
        request.setDisplayName(null);
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("displayName"));
    }

    @Test
    void postProfile_nullLegalName_failure() throws Exception {
        request.setLegalName(null);
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("legalName"));
    }

    @Test
    void postProfile_emptyCrmId_failure() throws Exception {
        request.setCrmId("");
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("crmId"));
    }

    @Test
    void postProfile_emptyDisplayName_failure() throws Exception {
        request.setDisplayName("");
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("displayName"));
    }

    @Test
    void postProfile_emptyLegalName_failure() throws Exception {
        request.setLegalName("");
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("legalName"));
    }

    @Test
    void postProfile_whitespaceCrmId_failure() throws Exception {
        request.setCrmId("  ");
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("crmId"));
    }

    @Test
    void postProfile_whitespaceDisplayName_failure() throws Exception {
        request.setDisplayName("  ");
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("displayName"));
    }

    @Test
    void postProfile_whitespaceLegalName_failure() throws Exception {
        request.setLegalName("  ");
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("legalName"));
    }

    @Test
    void postProfile_success() throws Exception {
        when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(request)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isCreated()).andReturn();
        CreateLedgerProfileResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), CreateLedgerProfileResponse.class);
        Assertions.assertNotNull(response);
    }

    @Test
    void postProfileWithExtraFields_failure() throws Exception {
        String body = "{\"crm_id\":\"69b5c9ac28d24500abd532c880918eda\",\"display_name\":\"TEST-DIS-413c1f7b-1222-4df5-852c-a702dfcd6696\",\"legal_name\":\"TEST-LEGAL\", \"legal_name1\":\"TEST-LEGAL\"}";
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(body).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        Assertions.assertTrue(result.getResponse().getContentAsString().contains("legal_name1"));
    }

    @Test
    void postSPCProfile_success() throws Exception {
        when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(requestSPC)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isCreated()).andReturn();
        CreateLedgerProfileResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), CreateLedgerProfileResponse.class);
        Assertions.assertNotNull(response);
    }

    @Test
    void postSPCProfile_missingToken_400_error() throws Exception {
        when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        //Remove the token trigger 400 error
        headers.remove(APICommonUtilConstant.AUTHORIZATION);
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(requestSPC)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isBadRequest()).andReturn();
        CreateLedgerProfileResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), CreateLedgerProfileResponse.class);
        Assertions.assertNotNull(response);
    }

    @Test
    void postSPCProfile_badToken_400_error() throws Exception {
        when(profileRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));
        //put bad token in trigger the 401 error
        headers.set(APICommonUtilConstant.AUTHORIZATION, ProfileTestUtil.BAD_JWT_TOKEN);
        MvcResult result = this.mockMvc.perform(
                MockMvcRequestBuilders.post(URL).headers(headers).content(objectMapper.writeValueAsString(requestSPC)).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)).andExpect(status().isUnauthorized()).andReturn();
        CreateLedgerProfileResponse response = objectMapper.readValue(result.getResponse().getContentAsString(), CreateLedgerProfileResponse.class);
        Assertions.assertNotNull(response);
    }
}

