import { CognitoAuth } from 'amazon-cognito-auth-js'

export const authData = {
    ClientId: process.env.REACT_APP_ClientId, // Your client id here
    AppWebDomain: process.env.REACT_APP_AppWebDomain, // Exclude the "https://" part.
    TokenScopesArray: process.env.REACT_APP_TokenScopesArray.split(','), // like ['openid','email','phone']...
    RedirectUriSignIn: process.env.REACT_APP_RedirectUriSignIn,
    RedirectUriSignOut: process.env.REACT_APP_RedirectUriSignOut,
    IdentityProvider: process.env.REACT_APP_IdentityProvider,
    UserPoolId: process.env.REACT_APP_UserPoolId,
    AdvancedSecurityDataCollectionFlag: false
}

const cognitoAuth = new CognitoAuth(authData)

cognitoAuth.useCodeGrantFlow()


if (!cognitoAuth.isUserSignedIn() && window.location.search) {
    const curUrl = window.location.href
    cognitoAuth.parseCognitoWebResponse(curUrl)

    cognitoAuth.userhandler = {
        onSuccess: (e) => {
            window.location.reload()
        },
        onFailure: (e) => {
            console.error(e)
        }
    }
    
}

export default cognitoAuth