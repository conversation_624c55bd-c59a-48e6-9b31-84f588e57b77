package com.peoplestrust.account.api.v1.service;

import com.peoplestrust.account.api.v1.AccountApplication;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.write.AccountRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ContextConfiguration(classes = AccountApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class UpdateAccountServiceTestIT {

  private static String profileId = UUID.randomUUID().toString();
  @Autowired
  AccountService accountService;

  @MockBean
  private AccountRepository accountRepository;

  UUID uuid = UUID.randomUUID();

  @Test
  public void updateAccount_success() throws Exception {
    Account account = TestUtil.createAccount(profileId);
    String profileIdAndAccountId = profileId + "_" + account.getRefId();
    Account putAccount =TestUtil.createUpdateAccount();

    AccountEntity accountEntity = new AccountEntity();
    accountEntity.setProfileId(UUID.fromString(profileId));
    accountEntity.setRefId(UUID.fromString(account.getRefId()));
    accountEntity.setStatus(AccountStatus.ACTIVE);

    OptionsEntity optionsEntity = new OptionsEntity();
    optionsEntity.setOverdraftAmount(new BigDecimal(100.00));
    optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
    accountEntity.setOptions(optionsEntity);

    when(accountRepository.findByRefId(any())).thenReturn(Optional.of(accountEntity));
    when(accountRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

    Account putReturnAccount =
        accountService.updateAccount(putAccount, account.getRefId(), profileId, profileIdAndAccountId);
    assertEquals(putAccount.getName(), putReturnAccount.getName());
    assertEquals(putAccount.getDescription(), putReturnAccount.getDescription());
    assertEquals(putAccount.getMonetaryUnit(), putReturnAccount.getMonetaryUnit());
  }
}
