import React from "react";
import Button from "components/Button";

import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";

import * as S from "./styled";

const Modal = (props) => {
  const {
    children,
    show,
    handleClose,
    title,
    onCreate,
    testId,
    createTxt,
    saveDisabled = false,
  } = props;

  return (
    <S.Dialog
      data-testid={testId || "modal"}
      open={show}
      onClose={handleClose}
      className={props.className || ""}
    >
      <DialogTitle data-testid="modal-title">
        <span className="modal-title-text" >{title}</span>
        <IconButton
          aria-label="close"
          onClick={handleClose}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent data-testid="modal-content">{children}</DialogContent>
      <DialogActions data-testid="modal-actions">
        {onCreate && (
          <Button
            type="primary"
            onClick={onCreate}
            testId="create-button"
            disabled={saveDisabled}
          >
            {createTxt || "Create"}
          </Button>
        )}
      </DialogActions>
    </S.Dialog>
  );
};

export default Modal;
