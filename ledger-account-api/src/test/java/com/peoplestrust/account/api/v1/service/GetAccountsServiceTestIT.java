package com.peoplestrust.account.api.v1.service;

import com.peoplestrust.account.api.v1.AccountApplication;
import com.peoplestrust.account.api.v1.common.TestUtil;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.AccountStatus;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.account.persistence.repository.read.ReadAccountRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@SpringBootTest
@ContextConfiguration(classes = AccountApplication.class)
@ExtendWith(SpringExtension.class)
@Slf4j
public class GetAccountsServiceTestIT {

    private static String profileId = UUID.randomUUID().toString();

    private static String interactionId = UUID.randomUUID().toString();
    @Autowired
    AccountService accountService;
    @MockBean
    private ValidationService validationService;

    @MockBean
    private ReadAccountRepository readAccountRepository;

    @Test
    public void getTest() throws Exception {
        Account account = TestUtil.createAccount(profileId);
        AccountEntity accountEntity = new AccountEntity();
        accountEntity.setProfileId(UUID.fromString(profileId));
        accountEntity.setRefId(UUID.fromString(account.getRefId()));
        accountEntity.setStatus(AccountStatus.ACTIVE);


        OptionsEntity optionsEntity = new OptionsEntity();
        optionsEntity.setOverdraftAmount(new BigDecimal(100.00));
        optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
        accountEntity.setOptions(optionsEntity);

        Integer numberAcct = 3;
        List<AccountEntity> accountEntities = TestUtil.getAccountEntityLst(account, profileId, numberAcct);

        when(readAccountRepository.findByProfileId(any())).thenReturn(accountEntities);
        when(validationService.isProfile(any(), any())).thenReturn(Boolean.TRUE);
        List<Account> accounts = accountService.getAccounts(profileId, interactionId);
        assertEquals(numberAcct, accounts.size());
    }

}
