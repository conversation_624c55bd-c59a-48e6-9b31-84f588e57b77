package com.peoplestrust.transaction.api.v1.service;

import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.InstructionStatus;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.util.api.common.exception.InvalidStatusTransitionException;
import com.peoplestrust.util.api.common.exception.InvalidFieldException;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import com.peoplestrust.util.api.common.exception.HttpClientErrorException;
import com.peoplestrust.util.api.common.util.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ExtendWith(MockitoExtension.class)
class RollbackTransactionServiceTest {

  private final String instructionRefId = UUID.randomUUID().toString();
  private final String profileId = UUID.randomUUID().toString();
  private final String accountId = UUID.randomUUID().toString();
  private final String interactionId = UUID.randomUUID().toString();

  @Mock
  private ValidationService validationService;
  @Mock
  private ReadInstructionRepository readInstructionRepository;
  @Mock
  private InstructionRepository instructionRepository;

  @InjectMocks
  private TransactionServiceImpl transactionService;

  private InstructionEntity pendingInstruction;
  private TransactionEntity pendingTransaction;
  private TransactionEntity rolledBackTransaction;

  @BeforeEach
  void setup() {
    pendingTransaction = TransactionEntity.builder()
        .transactionRefId(UUID.randomUUID().toString())
        .status(TransactionStatus.PENDING)
        .build();

    rolledBackTransaction = TransactionEntity.builder()
        .transactionRefId(UUID.randomUUID().toString())
        .status(TransactionStatus.ROLLBACKED)
        .build();

    pendingInstruction = InstructionEntity.builder()
        .instructionRefId(instructionRefId)
        .status(InstructionStatus.PENDING)
        .transactions(List.of(pendingTransaction))
        .build();
  }

  @Test
  void shouldRollbackTransactionSuccessfully() throws Exception {
    LocalDateTime expectedDateTime = LocalDateTime.of(2024, 1, 15, 10, 30, 0);
    OffsetDateTime mockOffsetDateTime = OffsetDateTime.of(expectedDateTime, ZoneOffset.UTC);

    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenReturn(true);

    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        UUID.fromString(profileId), UUID.fromString(accountId), instructionRefId))
        .thenReturn(pendingInstruction);

    try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
      dateUtilsMock.when(DateUtils::offsetDateTime).thenReturn(mockOffsetDateTime);

      transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId);

      assertThat(pendingTransaction.getStatus()).isEqualTo(TransactionStatus.ROLLBACKED);
      assertThat(pendingTransaction.getFinalizationDateTime()).isEqualTo(expectedDateTime);
      assertThat(pendingInstruction.getStatus()).isEqualTo(InstructionStatus.ROLLBACKED);

      verify(instructionRepository).save(pendingInstruction);
      dateUtilsMock.verify(DateUtils::offsetDateTime, times(1));
    }
  }

  @Test
  void shouldRollbackMultipleTransactionsWithMixedStatuses() throws Exception {
    pendingInstruction.setTransactions(List.of(pendingTransaction, rolledBackTransaction));

    LocalDateTime expectedDateTime = LocalDateTime.of(2024, 2, 10, 14, 45, 30);
    OffsetDateTime mockOffsetDateTime = OffsetDateTime.of(expectedDateTime, ZoneOffset.UTC);

    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenReturn(true);

    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        UUID.fromString(profileId), UUID.fromString(accountId), instructionRefId))
        .thenReturn(pendingInstruction);

    try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
      dateUtilsMock.when(DateUtils::offsetDateTime).thenReturn(mockOffsetDateTime);

      transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId);

      assertThat(pendingTransaction.getStatus()).isEqualTo(TransactionStatus.ROLLBACKED);
      assertThat(pendingTransaction.getFinalizationDateTime()).isEqualTo(expectedDateTime);

      assertThat(rolledBackTransaction.getStatus()).isEqualTo(TransactionStatus.ROLLBACKED);
      assertThat(rolledBackTransaction.getFinalizationDateTime()).isEqualTo(expectedDateTime);

      assertThat(pendingInstruction.getStatus()).isEqualTo(InstructionStatus.ROLLBACKED);

      verify(instructionRepository).save(pendingInstruction);
      dateUtilsMock.verify(DateUtils::offsetDateTime, times(2));
    }
  }

  @Test
  void shouldRollbackWhenInstructionIsAlreadyRollbacked() throws Exception {
    pendingInstruction.setStatus(InstructionStatus.ROLLBACKED);

    LocalDateTime expectedDateTime = LocalDateTime.of(2024, 3, 5, 9, 15, 45);
    OffsetDateTime mockOffsetDateTime = OffsetDateTime.of(expectedDateTime, ZoneOffset.UTC);

    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenReturn(true);

    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        UUID.fromString(profileId), UUID.fromString(accountId), instructionRefId))
        .thenReturn(pendingInstruction);

    try (MockedStatic<DateUtils> dateUtilsMock = mockStatic(DateUtils.class)) {
      dateUtilsMock.when(DateUtils::offsetDateTime).thenReturn(mockOffsetDateTime);

      transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId);

      assertThat(pendingTransaction.getStatus()).isEqualTo(TransactionStatus.ROLLBACKED);
      assertThat(pendingTransaction.getFinalizationDateTime()).isEqualTo(expectedDateTime);
      assertThat(pendingInstruction.getStatus()).isEqualTo(InstructionStatus.ROLLBACKED);

      verify(instructionRepository).save(pendingInstruction);
      dateUtilsMock.verify(DateUtils::offsetDateTime, times(1));
    }
  }

  @Test
  void shouldThrowInvalidStatusTransitionExceptionWhenTransactionStatusInvalid() throws Exception {
    pendingTransaction.setStatus(TransactionStatus.POSTED);

    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenReturn(true);

    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        UUID.fromString(profileId), UUID.fromString(accountId), instructionRefId))
        .thenReturn(pendingInstruction);

    assertThrows(InvalidStatusTransitionException.class, () ->
        transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId)
    );

    assertThat(pendingTransaction.getFinalizationDateTime()).isNull();
    verify(instructionRepository, never()).save(any());
  }

  @Test
  void shouldThrowResourceNotFoundExceptionWhenInstructionNotFound() throws Exception {
    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenReturn(true);

    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(
        UUID.fromString(profileId), UUID.fromString(accountId), instructionRefId))
        .thenReturn(null);

    assertThrows(ResourceNotFoundException.class, () ->
        transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId)
    );

    verify(instructionRepository, never()).save(any());
  }

  @Test
  void shouldThrowInvalidFieldExceptionWhenValidationServiceThrowsInvalidFieldException() throws Exception {
    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenThrow(new InvalidFieldException("Invalid profile or account"));

    assertThrows(InvalidFieldException.class, () ->
        transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId)
    );

    verify(readInstructionRepository, never()).findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any());
    verify(instructionRepository, never()).save(any());
  }

  @Test
  void shouldThrowHttpClientErrorExceptionWhenSpringHttpClientErrorOccurs() throws Exception {
    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenThrow(new org.springframework.web.client.HttpClientErrorException(
            org.springframework.http.HttpStatus.BAD_REQUEST, "Bad request"));

    assertThrows(HttpClientErrorException.class, () ->
        transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId)
    );

    verify(readInstructionRepository, never()).findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any());
    verify(instructionRepository, never()).save(any());
  }

  @Test
  void shouldThrowNullPointerExceptionWhenNullPointerExceptionOccurs() throws Exception {
    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenThrow(new NullPointerException("Null pointer"));

    assertThrows(NullPointerException.class, () ->
        transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId)
    );

    verify(readInstructionRepository, never()).findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any());
    verify(instructionRepository, never()).save(any());
  }

  @Test
  void shouldThrowGenericExceptionWhenUnexpectedExceptionOccurs() throws Exception {
    when(validationService.validateProfileAndAccount(accountId, profileId, interactionId))
        .thenThrow(new RuntimeException("Unexpected error"));

    assertThrows(Exception.class, () ->
        transactionService.rollbackTransaction(instructionRefId, profileId, accountId, interactionId)
    );

    verify(readInstructionRepository, never()).findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any());
    verify(instructionRepository, never()).save(any());
  }
}
