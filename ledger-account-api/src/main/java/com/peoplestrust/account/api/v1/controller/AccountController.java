package com.peoplestrust.account.api.v1.controller;

import com.peoples.banking.util.logger.annotation.PerfLogger;
import com.peoplestrust.account.api.v1.config.AccountProperty;
import com.peoplestrust.account.api.v1.mapper.AccountMapper;
import com.peoplestrust.account.api.v1.mapper.AccountResponseMapper;
import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.api.v1.model.Options;
import com.peoplestrust.account.api.v1.service.AccountService;
import com.peoplestrust.account.domain.model.CreateLedgerAccountRequest;
import com.peoplestrust.account.domain.model.CreateLedgerAccountResponse;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceHistoryResponse;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceResponse;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountResponse;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountsResponse;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountsResponseAccountsInner;
import com.peoplestrust.account.domain.model.UpdateLedgerAccountRequest;
import com.peoplestrust.account.domain.model.UpdateLedgerAccountResponse;
import com.peoplestrust.account.domain.model.UpdateLedgerAccountStatusRequest;
import com.peoplestrust.account.persistence.repository.write.AccountRepository;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.config.ErrorProperty;
import com.peoplestrust.util.api.common.controller.InternalAPIController;
import com.peoplestrust.util.api.common.exception.ValidationException;
import com.peoplestrust.util.api.common.util.JwtUtil;
import com.peoplestrust.util.api.common.util.Messages;
import com.peoplestrust.util.api.common.util.Utils;
import com.peoplestrust.util.api.common.validation.Validations;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller for Account API
 */
@RestController
@RequestMapping(value = "/v1")
@Slf4j
public class AccountController extends InternalAPIController {

  /**
   * Repository
   */
  @Autowired
  AccountRepository accountRepository;

  /**
   * Service layer
   */
  @Autowired
  private AccountService accountService;

  /**
   * Domain to DTO or DTO to Domain mapper layer
   */
  @Autowired
  private AccountResponseMapper accountResponseMapper;

  /**
   * Domain to DTO or DTO to Domain mapper layer
   */
  @Autowired
  private AccountMapper accountMapper;

  /**
   * Properties
   */
  @Autowired
  private AccountProperty accountProperty;

  @Autowired
  private Environment env;


  /**
   * Controller for Phase 1 :: Create
   *
   * @param interactionId      interaction ID header
   * @param interactionTime    interaction timestamp header
   * @param profileId          profile ID header
   * @param accountPostRequest account ID header
   * @param bindingResult      schema
   * @return CreateLedgerAccountResponse
   */
  @PerfLogger
  @RequestMapping(value = "/ledger/account", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.POST)
  public ResponseEntity<CreateLedgerAccountResponse> createAccount(
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileId,
      @Valid @RequestBody CreateLedgerAccountRequest accountPostRequest, BindingResult bindingResult) throws Exception {

    //error checking
    if (bindingResult.hasErrors()) {
      Validations.reply(bindingResult);
    }

    // initial header validations
    initialCheck(interactionTime, interactionId, profileId);

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS});

    // transformation layer -- domain to DTO
    Account requestDTO = accountMapper.fromAccountPostRequestToAccount(accountPostRequest);

    // service layer
    Account responseDto = accountService.createAccount(requestDTO, profileId, interactionId);

    // transformation layer -- domain (request) to DTO (response)
    CreateLedgerAccountResponse accountPostResponse = null;
    Options Options = null;
    accountPostResponse = accountResponseMapper.fromAccountToAccountPostResponse(responseDto);

    return new ResponseEntity<>(accountPostResponse, HttpStatus.CREATED);
  }

  /**
   * Controller to update an Account Status
   *
   * @param interactionId      interaction ID header
   * @param interactionTime    interaction timestamp header
   * @param profileId          profile ID header
   * @param accountRefId       account ID header
   * @param accountPostRequest request payload
   * @param bindingResult      schema
   * @return nothing
   */
  @PerfLogger
  @RequestMapping(value = "/ledger/account/{account_ref_id}/status", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.PATCH)
  public ResponseEntity updateAccountStatus(
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileId,
      @PathVariable(name = APICommonUtilConstant.ACCOUNT_REF_ID) String accountRefId,
      @Valid @RequestBody UpdateLedgerAccountStatusRequest accountPostRequest, BindingResult bindingResult) throws Exception {

    // error checking
    if (bindingResult.hasErrors()) {
      Validations.reply(bindingResult);
    }

    // initial header validations
    initialCheck(interactionTime, interactionId, profileId);

    String profileIdAndAccountId = profileId + "_" + accountRefId;

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS});

    // service layer
    Optional<Account> account = accountService.updateAccountStatus(accountRefId, accountPostRequest.getStatus(), accountPostRequest.getReason(), profileId,
        profileIdAndAccountId);

    // response
    return account.map((r) -> new ResponseEntity<Void>(HttpStatus.NO_CONTENT)).orElse(new ResponseEntity<Void>(HttpStatus.NOT_FOUND));
  }

  /**
   * Controller to update an Account
   *
   * @param interactionId        interaction ID header
   * @param interactionTime      interaction timestamp header
   * @param profileId            profile ID header
   * @param refId                account ref ID header ???
   * @param accountUpdateRequest request payload
   * @param bindingResult        payload request
   * @return nothing
   */
  @PerfLogger
  @RequestMapping(value = "/ledger/account/{account_ref_id}", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.PUT)
  public ResponseEntity<?> updateAccount(
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileId,
      @PathVariable(value = APICommonUtilConstant.ACCOUNT_REF_ID) String refId,
      @Valid @RequestBody UpdateLedgerAccountRequest accountUpdateRequest, BindingResult bindingResult) throws Exception {

    //error checking
    if (bindingResult.hasErrors()) {
      Validations.reply(bindingResult);
    }

    // initial header validation
    initialCheck(interactionTime, interactionId, profileId);

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS});

    // transformation layer -- domain to DTO
    Account requestDto = accountMapper.fromAccountPutRequestToAccount(accountUpdateRequest);

    String profileIdAndAccountId = profileId + "_" + refId;
    // service layer -- get account
    Account responseDto = accountService.updateAccount(requestDto, refId, profileId, profileIdAndAccountId);

    //transformation layer -- domain (request) to DTO (request)
    UpdateLedgerAccountResponse accountUpdateResponse = null;
    Options Options = null;
    accountUpdateResponse = accountResponseMapper.fromAccountToAccountPutResponse(responseDto);

    //response
    return new ResponseEntity<>(accountUpdateResponse, HttpStatus.OK);
  }

  /**
   * Controller to get Accounts related to a profile ID
   *
   * @param interactionId   interaction ID header
   * @param interactionTime interaction timestamp header
   * @param profileId       profile ID header
   * @return RetrieveLedgerAccountsResponse
   */
  @PerfLogger
  @RequestMapping(value = "/ledger/account", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
  public ResponseEntity<RetrieveLedgerAccountsResponse> getAccount(
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileId) throws Exception {

    // initial header validation
    initialCheck(interactionTime, interactionId, profileId);

    // response entity
    RetrieveLedgerAccountsResponse retrieveResponse = new RetrieveLedgerAccountsResponse();

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer -- get account
    List<Account> accountResponseDtoList = accountService.getAccounts(profileId, interactionId);

    // transformation layer -- domain (request) to DTO (request)
    List<RetrieveLedgerAccountsResponseAccountsInner> accounts =
        accountResponseMapper.fromAccountsToRetrieveLedgerAccountsResponse(accountResponseDtoList);

    retrieveResponse.accounts(accounts);

    //response
    return new ResponseEntity<>(retrieveResponse, HttpStatus.OK);
  }

  /**
   * Controller to get Account by account reference ID
   *
   * @param interactionId   interaction ID header
   * @param interactionTime interaction timestamp header
   * @param profileId       profile ID header
   * @param refId           account reference ID header
   * @return RetrieveLedgerAccountResponse
   */
  @PerfLogger
  @RequestMapping(value = "/ledger/account/{account_ref_id}", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
  public ResponseEntity<RetrieveLedgerAccountResponse> getAccountById(
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileId,
      @PathVariable(value = APICommonUtilConstant.ACCOUNT_REF_ID) String refId)
      throws Exception {

    // initial header validations
    initialCheck(interactionTime, interactionId, profileId);
    String profileIdAndAccountId = profileId + "_" + refId;

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer -- get account
    Account accountResponseDto = accountService.getAccountById(refId, profileId, profileIdAndAccountId);

    // transformation layer -- domain (request) to DTO (request)
    RetrieveLedgerAccountResponse retrieveLedgerAccountResponse = null;
    retrieveLedgerAccountResponse = accountResponseMapper.fromAccountToAccountGetResponse(accountResponseDto);

    //response
    return new ResponseEntity<>(retrieveLedgerAccountResponse, HttpStatus.OK);
  }


  /**
   * Controller to retrieve the account balance
   *
   * @param interactionId   interaction ID header
   * @param interactionTime interaction timestamp header
   * @param profileRefId    profile ID header
   * @param accountRefId    account reference ID header
   * @return RetrieveLedgerAccountBalanceResponse
   */
  @RequestMapping(value = "/ledger/account/{account_ref_id}/balance", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
  public ResponseEntity<RetrieveLedgerAccountBalanceResponse> retrieveTransaction(
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileRefId,
      @PathVariable(name = APICommonUtilConstant.ACCOUNT_REF_ID, required = true) String accountRefId) throws Exception {

    // initial header validations
    initialCheck(interactionTime, interactionId, profileRefId);

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer
    RetrieveLedgerAccountBalanceResponse response = accountService.retrieveLedgerAccountBalance(profileRefId, accountRefId, interactionId);

    // response
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  /**
   * Controller to retrieve history of previous 48 balances.
   *
   * @param interactionId   unique ID header
   * @param interactionTime unique timestamp header
   * @param profileId       unique profile header
   * @param accountRefId    unique account header
   * @return List of RetrieveLedgerAccountBalanceHistoryResponse
   */
  @PerfLogger
  @RequestMapping(value = "/ledger/account/{account_ref_id}/balance/all", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
  public ResponseEntity<List<RetrieveLedgerAccountBalanceHistoryResponse>> getLatest48AccountBalances(
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileId,
      @PathVariable(name = APICommonUtilConstant.ACCOUNT_REF_ID, required = true) String accountRefId) throws Exception {

    // initial header validation
    initialCheck(interactionTime, interactionId, profileId);

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer -- get account
    List<BalanceSnapshotResponse> balanceSnapshotResponseList = accountService.retrieve48Snapshots(profileId, accountRefId, interactionId);

    // transformation layer -- domain (request) to DTO (request)
    List<RetrieveLedgerAccountBalanceHistoryResponse> balanceHistoryResponse =
        accountResponseMapper.fromBalanceSnapshotResponseListToRetrieveLedgerAccountBalanceHistoryResponseList(balanceSnapshotResponseList);

    //response
    return new ResponseEntity<>(balanceHistoryResponse, HttpStatus.OK);
  }

  /**
   * Controller to search previous balances.
   *
   * @param interactionId    unique ID header
   * @param interactionTime  unique timestamp header
   * @param profileId        unique profile header
   * @param accountRefId     unique account header
   * @param startTime        the start date time range
   * @param endTime          the end data time range
   * @param offset           offset is starting point of payments request filter; if offset is not provided it would be defaulted to zero
   * @param maxResponseItems Maximum number of response items to be returned. All items are returned if this field is absent
   * @return List of RetrieveLedgerAccountBalanceHistoryResponse
   */
  @PerfLogger
  @RequestMapping(value = "/ledger/account/balance_search/{account_ref_id}", produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
  public ResponseEntity<List<RetrieveLedgerAccountBalanceHistoryResponse>> searchAccountBalancesSnapshots(
      @RequestHeader(name = APICommonUtilConstant.AUTHORIZATION_HEADER, required = true) String authorization,
      @RequestHeader(name = APICommonUtilConstant.HEADER_AMAZON_REQUEST_ID, required = false) String requestId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_ID, required = true) String interactionId,
      @RequestHeader(name = APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, required = true) String interactionTime,
      @RequestHeader(name = APICommonUtilConstant.HEADER_PROFILE_ID, required = true) String profileId,
      @PathVariable(name = APICommonUtilConstant.ACCOUNT_REF_ID, required = true) String accountRefId,
      @RequestParam(name = APICommonUtilConstant.START_TIME, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime startTime,
      @RequestParam(name = APICommonUtilConstant.END_TIME, required = false) @DateTimeFormat(pattern = APICommonUtilConstant.UTC_DATE_TIME_FORMAT) LocalDateTime endTime,
      @RequestParam(name = APICommonUtilConstant.OFFSET, required = false, defaultValue = "0") Integer offset,
      @RequestParam(name = APICommonUtilConstant.MAX_ITEMS, required = false, defaultValue = "25") @Max(25) Integer maxResponseItems) throws Exception {

    // initial header validation
    initialCheck(interactionTime, interactionId, profileId);

    if (!Utils.isValidUUID(accountRefId)) {
      throw new ValidationException(ErrorProperty.INVALID_FIELD.name(), APICommonUtilConstant.ACCOUNT_ID + " is invalid input");
    }

    if (maxResponseItems < 1 || maxResponseItems > 250) {
      throw new ValidationException(ErrorProperty.INVALID_FIELD.name(), APICommonUtilConstant.MAX_ITEMS + " is invalid input");
    }

    if (startTime != null && endTime != null) {
      if (startTime.isAfter(endTime) || startTime.isEqual(endTime)) {
        log.error("The startTime must be before endTime. startTime: {}, endTime: {}", startTime, endTime);
        throw new ValidationException(Messages.END_TIME_LESS_THAN_START_TIME);
      }
    }

    //check user group
    checkJwtTokenAndVerifyGroup(authorization, new String[]{APICommonUtilConstant.GL_ADMINISTRATION,
        APICommonUtilConstant.GL_OPERATIONS, APICommonUtilConstant.GL_SERVICE_TEAM});

    // service layer -- get account
    List<BalanceSnapshotResponse> balanceSnapshotResponseList = accountService.searchBalanceSnapshots(profileId, accountRefId, interactionId, startTime, endTime, offset, maxResponseItems);

    // transformation layer -- domain (request) to DTO (request)
    List<RetrieveLedgerAccountBalanceHistoryResponse> balanceHistoryResponse =
        accountResponseMapper.fromBalanceSnapshotResponseListToRetrieveLedgerAccountBalanceHistoryResponseList(balanceSnapshotResponseList);

    //response
    return new ResponseEntity<>(balanceHistoryResponse, HttpStatus.OK);
  }

  /**
   * @return timeToLive
   */
  @Override
  protected int getApiTimeToLiveValue() {
    return accountProperty.getTimeToLive();
  }

  private void checkJwtTokenAndVerifyGroup(String authorization, String[] checkUserGroups) {
    String disableJwt = env.getProperty(APICommonUtilConstant.ENVIRONMENT_VARIABLE_DISABLE_COGNITO_JWT_VERIFICATION);
    if (!Boolean.parseBoolean(disableJwt)) {
      JwtUtil.isTokenValidAndVerifyGroup(authorization, checkUserGroups);
    }
  }
}
