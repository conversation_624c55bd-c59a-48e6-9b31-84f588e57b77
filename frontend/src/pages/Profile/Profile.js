import { useState } from 'react' 
import Details from 'components/Details'
import Button from 'components/Button'
import useAuth from 'hooks/useAuth'
import { useParams } from 'react-router-dom'
import profileDetails from 'constants/details/profile'
import accountColumns from 'constants/columns/accounts'
import CreateAccount from 'modules/popups/CreateAccount'
import CreateProfile from 'modules/popups/CreateProfile'
import useGetProfileById from 'hooks/Profile/useGetProfileById'
import useGetAccounts from 'hooks/Account/useGetAccounts'
import Table from 'components/Table'
import { GL_OPERATIONS, GL_ADMINISTRATION } from 'constants/cognitoGroupsTypes'
import * as S from './styled'

const Profile = () => {
    const { isAllowed } = useAuth()
    const { profileId } = useParams()
    const data = useGetProfileById(profileId)
    const accounts = useGetAccounts(profileId)
    const [show, setShow] = useState(false)

    const createIsAllowed = isAllowed([GL_OPERATIONS, GL_ADMINISTRATION])

    return (
        <S.Profile data-testid='page-container'>
            <S.ProfileHeader>
                <h2 data-testid='page-title'>
                    { data?.legal_name }
                </h2>
                {
                    createIsAllowed && (
                        <Button type='primary' onClick={() => setShow(true)} testId='update-button'>
                            Update Profile
                        </Button>
                    )
                }
            </S.ProfileHeader>
            <Details data={data} details={profileDetails} />
            <Table
                columns={accountColumns(profileId)}
                data={accounts}
                getRowId={(row) => row.ref_Id}
                total={accounts?.length}
                createModal={createIsAllowed && CreateAccount} 
                modalParentId={profileId}
                createText='Create Account'
            />
            {
                createIsAllowed && (
                    <CreateProfile
                        show={show}
                        handleClose={() => setShow(false)}
                        data={data}
                        edit={true}
                        testId='update-profile-modal'
                    />
                )
            }
            
        </S.Profile>
    )
}

export default Profile