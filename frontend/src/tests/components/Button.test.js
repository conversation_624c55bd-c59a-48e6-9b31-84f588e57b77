import { render, fireEvent } from '@testing-library/react'
import Button from 'components/Button'
import findByTestId from 'utils/findByTestId'

const setUp = (props) => {
    render(
        <Button {...props} />
    )
}


describe('Button component', () => {

    it('should render without errors', () => {
        setUp()
        const button = findByTestId('button-default')
        expect(button).toBeInTheDocument()
    })

    it('should render with text', () => {
        setUp({ children: 'Hello' })
        const button = findByTestId('button-default')
        expect(button).toHaveTextContent('Hello')
    })

    it ('should render primary button', () => {
        setUp({ type: 'primary' })
        const button = findByTestId('button-primary')
        expect(button).toBeInTheDocument()
    })

    it ('should trigger onClick event', () => {
        const handleClick = jest.fn()
        setUp({ onClick: handleClick })
        const button = findByTestId('button-default')
        fireEvent.click(button)
        expect(handleClick).toHaveBeenCalledTimes(1)
    })


})