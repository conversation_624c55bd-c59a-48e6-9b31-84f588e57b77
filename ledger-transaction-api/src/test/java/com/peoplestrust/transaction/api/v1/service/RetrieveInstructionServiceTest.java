package com.peoplestrust.transaction.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.peoplestrust.scheduler.persistence.entity.BalanceEntity;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.scheduler.persistence.repository.write.BalanceRepository;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.config.TransactionProperty;
import com.peoplestrust.transaction.api.v1.mapper.TransactionMapper;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.model.Transaction;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.MonetaryUnit;
import com.peoplestrust.transaction.persistence.entity.PaymentCategoryType;
import com.peoplestrust.transaction.persistence.entity.PaymentRailType;
import com.peoplestrust.transaction.persistence.entity.TransactionEntity;
import com.peoplestrust.transaction.persistence.entity.TransactionFlowType;
import com.peoplestrust.transaction.persistence.entity.TransactionHoldType;
import com.peoplestrust.transaction.persistence.repository.read.ReadInstructionRepository;
import com.peoplestrust.transaction.persistence.repository.read.ReadTransactionRepository;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.transaction.persistence.repository.write.TransactionRepository;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class RetrieveInstructionServiceTest {


  @Mock
  private TransactionRepository transactionRepository;
  @Mock
  private ValidationService validationService;
  @InjectMocks
  private TransactionServiceImpl transactionService;
  @Mock
  private TransactionMapper transactionMapper;

  @Mock
  private InstructionRepository instructionRepository;
  @Mock
  private ReadInstructionRepository readInstructionRepository;

  @Mock
  private ReadBalanceRepository readBalanceRepository;

  @Mock
  TransactionProperty transactionProperty;

  String accountId = UUID.randomUUID().toString();
  String interactionId = UUID.randomUUID().toString();
  private static String profileId = UUID.randomUUID().toString();


  @Test
  public void retrieveInstruction() throws Exception {
    Instruction instruction = createInstruction();
    Instruction retInstruction = createInstruction();
    Account account = TestUtil.createAccount(accountId, profileId);
    BalanceEntity balanceEntity = getBalanceEntity();
    InstructionEntity instructionEntity = getInstructionData(instruction);
    List<TransactionEntity> tlist = getTransactionEntities(instruction);
    when(instructionRepository.save(any())).thenReturn(instructionEntity);
    when(transactionRepository.saveAll(any())).thenReturn(tlist);
    when(readInstructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any())).thenReturn(instructionEntity);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(Boolean.TRUE);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.validateProfileAndAccount(any(), any(), any())).thenReturn(true);
    when(transactionMapper.fromInstructionToInstructionEntity(any())).thenReturn(instructionEntity);
    when(transactionMapper.fromInstructionEntityToInstruction(any())).thenReturn(retInstruction);
    when(transactionMapper.fromTransactionToTransactionEntity(any())).thenReturn(tlist.get(1));
    when(readBalanceRepository.findFirstByAccountRefIdAndProfileRefIdOrderByCreatedDateTimeDesc(any(), any())).thenReturn(Optional.of(balanceEntity));
    when(transactionProperty.getUseStoreProcedureCalculateSum()).thenReturn(Boolean.FALSE);
    Instruction savedInstruction = transactionService.initiateInstruction(instruction, profileId, accountId, interactionId);
    Instruction returnInstruction = transactionService.getInstruction(profileId, accountId, savedInstruction.getInstructionRefId(), interactionId);
    assertEquals(savedInstruction.getInstructionRefId(), returnInstruction.getInstructionRefId());
  }

  private Instruction createInstruction() {
    List<Transaction> transactions = createTransactions();

    Instruction instruction = Instruction.builder().instructionRefId("TEST_INS" + UUID.randomUUID().toString()).accountRefId(accountId)
        .paymentRail(PaymentRailType.EFT).profileRefId(profileId).transactions(transactions)
        .createdDateTime(DateUtils.offset()).updatedDateTime(DateUtils.offset()).build();
    instruction.setTransactions(transactions);
    return instruction;
  }

  private List<Transaction> createTransactions() {
    List<Transaction> list = new ArrayList<>();

    Transaction t1 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit("CAD").acceptanceDateTime(DateUtils.offsetDateTime())
        .dueDateTime(DateUtils.offsetDateTime().plusDays(2)).build();

    Transaction t2 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.DEBIT)
        .paymentCategory(PaymentCategoryType.CREDIT_PUSH).amount(new BigDecimal(100)).monetaryUnit("CAD").acceptanceDateTime(DateUtils.offsetDateTime())
        .build();

    Transaction t3 = Transaction.builder().transactionRefId(UUID.randomUUID().toString()).transactionFlow(TransactionFlowType.CREDIT)
        .paymentCategory(PaymentCategoryType.DEBIT_PULL).amount(new BigDecimal(100)).monetaryUnit("CAD").acceptanceDateTime(DateUtils.offsetDateTime()).build();

    list.add(t1);
    list.add(t2);
    list.add(t3);
    return list;
  }

  private InstructionEntity getInstructionData(Instruction instruction) {
    InstructionEntity instructionEntity = new InstructionEntity();

    instructionEntity.setInstructionRefId(instruction.getInstructionRefId());
    instructionEntity.setStatus(instruction.getStatus());
    instructionEntity.setAccountRefId(UUID.fromString(instruction.getAccountRefId()));
    instructionEntity.setPaymentRail(instruction.getPaymentRail());
    instructionEntity.setProfileRefId(UUID.fromString(instruction.getProfileRefId()));

    instructionEntity.setTransactions(getTransactionEntities(instruction));
    return instructionEntity;
  }

  public List<TransactionEntity> getTransactionEntities(Instruction instruction) {
    List<TransactionEntity> ts = new ArrayList<>();
    instruction.getTransactions().forEach(t -> {
      TransactionEntity te = TransactionEntity.builder().transactionRefId(t.getTransactionRefId())
          .acceptanceDateTime(t.getAcceptanceDateTime().toLocalDateTime())
          .transactionFlow(t.getTransactionFlow()).paymentCategory(t.getPaymentCategory()).transactionHold(TransactionHoldType.HOLD)
          .monetaryUnit(MonetaryUnit.valueOf(t.getMonetaryUnit())).amount(t.getAmount()).build();
      ts.add(te);
    });
    return ts;
  }

  private BalanceEntity getBalanceEntity() {
    BalanceEntity balanceEntity = new BalanceEntity();
    balanceEntity.setTotalAmount(BigDecimal.valueOf(500));
    return balanceEntity;
  }

  @AfterEach
  public void doCleanUpAfterTest() {
    log.trace("cleanup - start");
    instructionRepository.findByProfileRefIdAndAccountRefId(UUID.fromString(profileId), UUID.fromString(accountId)).stream().
        forEach(e -> instructionRepository.delete(e));
    log.trace("clean up - end");
  }
}
