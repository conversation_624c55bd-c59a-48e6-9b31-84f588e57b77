import baseQueryOptions from 'constants/baseQueryOptions'
import useLazyQuery from 'hooks/useLazyQuery'
import { getInternalErrorDetails } from 'services/internal'

const useGetInternalError = (props) => {
    const [trigger, { data, isLoading }] = useLazyQuery(['getInternalError', props], () => getInternalErrorDetails(props), {
        keepPreviousData: true,
        ...baseQueryOptions
    })

    return { trigger, data, isLoading }

}

export default useGetInternalError