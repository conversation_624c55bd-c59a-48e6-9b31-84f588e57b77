apiVersion: v1
kind: Secret
metadata:
  name: profile-database
  namespace: pg-ledger-prod
type: Opaque
stringData:
  sys.database.host: "WRITE_DB_URL_NO_PORT" # relationship
  sys.database.read.host: "READ_DB_URL_NO_PORT" # relationship
  sys.database.name: "gl_db_prod"
  sys.database.schema: "relationship"
  sys.database.username: "relationship_user"
  sys.database.password: "PLACEHOLDER"
---
apiVersion: v1
kind: Secret
metadata:
  name: account-database
  namespace: pg-ledger-prod
type: Opaque
stringData:
  sys.database.host: "WRITE_DB_URL_NO_PORT" # account
  sys.database.read.host: "READ_DB_URL_NO_PORT" # account
  sys.database.name: "gl_db_prod"
  sys.database.schema: "account"
  sys.database.username: "account_user"
  sys.database.password: "PLACEHOLDER"
---
apiVersion: v1
kind: Secret
metadata:
  name: transaction-database
  namespace: pg-ledger-prod
type: Opaque
stringData:
  sys.database.host: "WRITE_DB_URL_NO_PORT" # transaction
  sys.database.read.host: "READ_DB_URL_NO_PORT" # transaction
  sys.database.name: "gl_db_prod"
  sys.database.schema: "transaction"
  sys.database.username: "transaction_user"
  sys.database.password: "PLACEHOLDER"

---
apiVersion: v1
kind: Secret
metadata:
  name: kafka
  namespace: pg-ledger-prod
type: Opaque
stringData:
  sys.kafka.host: "CHANGE_THIS"
  sys.kafka.sasl.username: "CHANGE_THIS"
  sys.kafka.sasl.password: "CHANGE_THIS"

---
apiVersion: v1
kind: Secret
metadata:
  name: aws-secret
  namespace: pg-ledger-prod
type: Opaque
stringData:
  aws.access.key: "placeHolder"
  aws.secret.key: "placeHolder"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: application-dependencies
  namespace: pg-ledger-prod
data:
  sys.profile.url: "http://profile-service.pg-ledger-prod.svc.cluster.local:8080/v1/internal/profile"
  sys.account.url: "http://account-service.pg-ledger-prod.svc.cluster.local:8080/v1/internal/account"
  sys.account.validate.url: "http://account-service.pg-ledger-prod.svc.cluster.local:8080/v1/internal/account/validate"
  sys.transaction.url: "http://transaction-service.pg-ledger-prod.svc.cluster.local:8080/v1/internal/transaction/balance"
  sys.accounts.retrieve.url: "http://account-service.pg-ledger-prod.svc.cluster.local:8080/v1/internal/accounts/all"
  sys.rollback.internal.url: "http://transaction-service.pg-ledger-prod.svc.cluster.local:8080/v1/internal/ledger/transaction/rollback"
  app.balance-snapshot.schedule: "0 0 1,6,18 ? * *"
  app.rollback-transactions.schedule: "0 0,20,40 0/1 ? * *"
  app.rollback-transactions.expiry.time: "5"
  app.instructions.fetch.limit: "250"
  app.use.store.procedure.for.sum: "false"
  app.ssl.enable: "true"
  app.kafka.topic: "PENDING_TRANSACTIONS"
  app.kafka.topic.v2: "PENDING_TRANSACTIONS_V2"
  app.kafka.error.topic: "MANUAL_INTERVENTION_TRANSACTIONS"
  app.kafka.failed.status.topic: "FAILED_TRANSACTIONS"
  app.kafka.consumer.group.id: "ASYNCHRONOUS_API"
  app.kafka.backoff.maxfailure: "3"
  app.kafka.backoff.interval: "2000"
  app.kafka.max.poll.idle: "1000"
  app.kafka.max.poll.interval: "700000"
  app.kafka.max.poll.records: "100"
  app.kafka.listener.number: "2"
  sys.gl.transaction.url: "http://transaction-service.pg-ledger-prod.svc.cluster.local:8080/v1/ledger/transaction"
  app.transaction.validation-service.timeout.connection: "2000"
  app.transaction.validation-service.timeout.read: "10000"
  app.transaction.api.timeout.connection: "5000"
  app.transaction.api.timeout.read: "10000"
  app.redis.read.timeout: "120"
  app.redis.connection.timeout: "30"
  app.redis.cache.expiry: "5"
  app.db.maximum.pool.size: "50"
  app.db.connection.timeout: "1000"
  app.account.validation-service.timeout.connection: "2000"
  app.account.validation-service.timeout.read: "10000"
  app.health.eks-name-space: "pg-ledger-prod"
  app.health.cloud-watch-log-group: "/production/prod/general-ledger/eks/gl-eks-main-prod/application"
  app.jwt.token.check.disable: "false"
  app.kafka.listener.v2: "true"
  app.async.transaction.v2: "true"
  app.async.transaction.producer.v2: "true"
  app.log.level.root: "INFO"
  app.log.level.com.peoplestrust: "INFO"
  app.log.level.hibernate: "INFO"
  app.log.level.api.payload: "INFO"
  app.log.level.perf: "INFO"
  app.log.level.flow: "INFO"
