package com.peoplestrust.transaction.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.transaction.api.v1.common.TestUtil;
import com.peoplestrust.transaction.api.v1.model.Account;
import com.peoplestrust.transaction.api.v1.model.Instruction;
import com.peoplestrust.transaction.api.v1.service.ValidationService;
import com.peoplestrust.transaction.domain.model.CommitLedgerTransactionResponse;
import com.peoplestrust.transaction.persistence.entity.InstructionEntity;
import com.peoplestrust.transaction.persistence.entity.InstructionStatus;
import com.peoplestrust.transaction.persistence.entity.TransactionStatus;
import com.peoplestrust.transaction.persistence.repository.write.InstructionRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import com.peoplestrust.util.api.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.Instant;
import java.util.UUID;

@Slf4j
@ActiveProfiles("test")
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class CommitInstructionControllerIT {

  private static final String URL_COMMIT_TRANSACTION = "/v1/ledger/transaction";
  private static final String interactionId = UUID.randomUUID().toString();
  private static final String profileRefId = UUID.randomUUID().toString();
  private static final String accountRefId = UUID.randomUUID().toString();

  @Autowired
  private MockMvc mockMvc;

  @MockBean
  private ValidationService validationService;

  @MockBean
  private InstructionRepository instructionRepository;

  private HttpHeaders headers;
  private ObjectMapper objectMapper;
  private InstructionEntity instructionEntity;

  @BeforeEach
  public void setupBeforeTest() {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
    headers.add(APICommonUtilConstant.HEADER_PROFILE_ID, profileRefId);
    headers.add(APICommonUtilConstant.HEADER_ACCOUNT_ID, accountRefId);
    headers.add(APICommonUtilConstant.AUTHORIZATION, TestUtil.JWT_TOKEN);

    instructionEntity = TestUtil.createInstructionAndTransactionData(accountRefId, profileRefId);
    instructionEntity.setInstructionRefId(UUID.randomUUID().toString());  // Ensure the instruction has a ref ID
  }

  @Test
  public void commitInstructionTest() throws Exception {
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);

    Instruction instruction = TestUtil.createInstruction(accountRefId, profileRefId);
    InstructionEntity instructionEntity = TestUtil.getInstructionData(instruction);
    //Change transaction status to pending
    for (int i = 0; i < instructionEntity.getTransactions().size(); i++) {
      instructionEntity.getTransactions().get(i).setStatus(TransactionStatus.PENDING);
    }

    when(instructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any())).thenReturn(instructionEntity);
    when(instructionRepository.save(any())).thenAnswer(invocation -> invocation.getArgument(0));

    CommitLedgerTransactionResponse response = commitInstruction();
    assertNotNull(response);
    assertEquals(instructionEntity.getInstructionRefId(), response.getInstructionRefId());
  }

  private CommitLedgerTransactionResponse commitInstruction() throws Exception {
    MvcResult result = this.mockMvc.perform(
            MockMvcRequestBuilders.patch(URL_COMMIT_TRANSACTION + "/" + instructionEntity.getInstructionRefId())
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andReturn();

    return JsonUtil.toObject(result.getResponse().getContentAsString(), CommitLedgerTransactionResponse.class);
  }

  @Test
  public void commitInstruction_ShouldUpdateStatusAndSetFinalizationDateTime() throws Exception {
    // --- ARRANGE ---

    // 1. Create the initial instruction entity that the repository will "find"
    InstructionEntity initialInstructionEntity = TestUtil.createInstructionAndTransactionData(accountRefId, profileRefId);
    initialInstructionEntity.setInstructionRefId(UUID.randomUUID().toString());
    initialInstructionEntity.setStatus(com.peoplestrust.transaction.persistence.entity.InstructionStatus.PENDING);

    // 2. Set the transactions to PENDING status so they are eligible for update
    initialInstructionEntity.getTransactions().forEach(t -> t.setStatus(TransactionStatus.PENDING));

    // 3. Mock the repository calls
    //    - When findBy... is called, return our initial entity
    when(instructionRepository.findByProfileRefIdAndAccountRefIdAndInstructionRefId(any(), any(), any()))
        .thenReturn(initialInstructionEntity);
    //    - When save is called, return the entity that was passed to it (simulates a successful save)
    when(instructionRepository.save(any(InstructionEntity.class)))
        .thenAnswer(invocation -> invocation.getArgument(0));

    // 4. Mock the validation service to allow the call to proceed
    when(validationService.isProfileAvailable(any(), any())).thenReturn(true);
    Account account = TestUtil.createAccount(accountRefId, profileRefId);
    when(validationService.getAccount(any(), any(), any())).thenReturn(account);

    // --- ACT ---
    // Perform the API call via MockMvc
    this.mockMvc.perform(
            MockMvcRequestBuilders.patch(URL_COMMIT_TRANSACTION + "/" + initialInstructionEntity.getInstructionRefId())
                .headers(headers)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());

    // --- ASSERT ---

    // 1. Create an ArgumentCaptor to capture the entity passed to instructionRepository.save()
    ArgumentCaptor<InstructionEntity> instructionCaptor = ArgumentCaptor.forClass(InstructionEntity.class);

    // 2. Verify that the save method was called exactly once and capture the argument
    verify(instructionRepository).save(instructionCaptor.capture());

    // 3. Get the captured entity
    InstructionEntity savedEntity = instructionCaptor.getValue();

    // 4. Assert the state of the captured entity
    assertNotNull(savedEntity);
    // Assert the parent instruction status was updated
    assertEquals(InstructionStatus.POSTED, savedEntity.getStatus(), "Instruction status should be updated to POSTED.");

    // Assert the child transactions were updated
    assertNotNull(savedEntity.getTransactions());
    assertFalse(savedEntity.getTransactions().isEmpty(), "Transactions list should not be empty.");

    // **NEW VERIFICATION**: Loop through transactions and check status and finalizationDateTime
    savedEntity.getTransactions().forEach(transaction -> {
      assertEquals(TransactionStatus.POSTED, transaction.getStatus(), "Transaction status should be POSTED.");
      assertNotNull(transaction.getFinalizationDateTime(), "FinalizationDateTime should now be set and not null.");
    });
  }

}
