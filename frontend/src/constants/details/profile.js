import generateDate from "utils/generateDate"

const details = [
    {
        title: 'Legal Name',
        key: 'legal_name'
    },
    {
        title: 'Display Name',
        key: 'display_name'
    },
    {
        title: 'Ref ID',
        key: 'ref_id'
    },
    {
        title: 'CRM ID',
        key: 'crm_id'
    },
    {
        title: 'Profile Status',
        key: 'status'
    },
    {
        title: 'Created Date',
        key: (item) => generateDate(item?.created_date_time) || 'N/A'
    },
    {
        title: 'Updated Date',
        key: (item) => generateDate(item?.updated_date_time) || 'N/A'
    },

]

export default details