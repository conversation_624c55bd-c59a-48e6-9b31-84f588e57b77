import { useEffect, useState } from 'react'
import Table from 'components/Table'
import useAuth from 'hooks/useAuth'
import ProfilesColumns from 'constants/columns/profiles'
import CreateProfile from 'modules/popups/CreateProfile'
import useGetProfiles from 'hooks/Profile/useGetProfiles'
import profilesFilter from 'constants/filters/profiles'
import useRequest from 'hooks/useRequest'
import { GL_OPERATIONS, GL_ADMINISTRATION } from 'constants/cognitoGroupsTypes'
import useSearchProfiles from 'hooks/Profile/useSearchProfile'
import _ from 'lodash'
import * as S from './styled'
import * as GS from '../../styled'

const Profiles = () => {
    const [value, setValue] = useState('')
    const { isAllowed } = useAuth()
    const { trigger, data: SearchedProfiles} = useSearchProfiles(value)
    const { data } = useRequest()
    const profilesList = useGetProfiles(data)
    const [filter, setFilter] = useState(null)

    const profiles = value === '' ? profilesList : SearchedProfiles?.profiles

    const createIsAllowed = isAllowed([GL_OPERATIONS, GL_ADMINISTRATION])

    const onFliter = _.debounce((value) => {
        if (value?.name?.length > 2) {
            setValue(value.name)
        } else {
            setValue('')
        }
    }, 300)

    useEffect(() => {
        if (value) {
            trigger()
        }
    }, [value])

    return (
        <GS.ContentConatiner data-testid='page-container'>
              <S.Profiles>
                <h2 data-testid='page-title'>Profiles</h2>
                <Table 
                    columns={ProfilesColumns}
                    data={filter || profiles}
                    getRowId={(row) => row.ref_id}
                    total={profiles?.length}
                    createModal={createIsAllowed && CreateProfile}
                    filters={profilesFilter}
                    filterForms={{ noButton: true, grid: 1}}
                    withOnChange
                    onFilter={onFliter}
                    createText='Create New Profile'
                />
            </S.Profiles>
        </GS.ContentConatiner>
      
    )
}

export default Profiles