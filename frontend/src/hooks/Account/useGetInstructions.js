import {
    useQuery
} from 'react-query'
import baseQueryOptions from 'constants/baseQueryOptions'
import { getInstructions } from 'services/transaction'

const useGetInstructions = (props, global) => {
    const { data, isLoading, refetch } = useQuery(['getInstructions', props, global], () => getInstructions(props, global), {
        keepPreviousData: true,
        ...baseQueryOptions,
    })

    return { data, isLoading, refetch }

}

export default useGetInstructions
