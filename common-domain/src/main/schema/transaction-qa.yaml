openapi: 3.0.2
paths:
  #########################################################################################################
  /v1/internal/transaction/balance/all/{account_ref_id}:
    patch:
      tags:
        - Ledger Profile
      operationId: createProfile
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchLedgerTransactionRequest'
        required: true
      responses:
        '201':
          description: Profile successfully created
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/RetrieveLedgerTransactions'
        '400':
          description: Bad Request
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '404':
          description: Resource not found
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '500':
          description: internal system error
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'
        '503':
          description: System is temporarily unavailable
          headers:
            x-pg-interaction-id:
              $ref: 'common/common.yaml#/components/headers/InteractionId'
          content:
            application/json:
              schema:
                $ref: 'common/common.yaml#/components/schemas/ErrorResponse'


components:
  schemas:
    PatchLedgerTransactionRequest:
      type: object
      required:
        - effective_date_time
      properties:
        effective_date_time:
          $ref: 'common/common.yaml#/components/schemas/EffectiveDateTime'
