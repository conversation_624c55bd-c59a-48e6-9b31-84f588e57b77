// src/mocks/mswHandlers/profilesHandler.js
import { rest } from 'msw';
import profiles from 'data/profiles';

const apiUrl = process.env.REACT_APP_API_URL;

export const fetch_profile = rest.get(`${apiUrl}profile`, async (req, res, ctx) => {
    return res(
        ctx.status(200),
        ctx.json(profiles) // Return the entire list
    );
});

export const fetch_profile_by_id = rest.get(`${apiUrl}profile/:id`, async (req, res, ctx) => {
    // Convert the URL param to a number
    const id = parseInt(req.params.id, 10);
    // Find the matching profile by numeric ID
    const result = profiles.find((data) => data.id === id);

    return res(
        ctx.status(200),
        // If not found, could return `null` or a 404. Here we'll just return `undefined` if no match.
        ctx.json(result)
    );
});

// Example new single profile
export const mockSingleProfile = {
    id: 4,
    profiles: '<PERSON> 4',
    displayName: 'Test Profile 4',
    path: 'johnDoe4',
    refId: '12345',
    legalName: '<PERSON>',
    createdDate: '2022/10/11',
    status: 'active',
    crmId: '123431',
};

// For creating a new profile
export const create_profile = rest.post(`${apiUrl}profile`, async (req, res, ctx) => {
    return res(
        ctx.status(200),
        ctx.json([...profiles, mockSingleProfile]) // returns existing + new
    );
});
