import { useQuery } from "react-query";
import baseQueryOptions from "constants/baseQueryOptions";
import { getTransactions } from "services/transaction";

const useGetTransaction = (props) => {
  const { data } = useQuery(
    ["getTransaction", props],
    () => getTransactions(props),
    {
      keepPreviousData: true,
      ...baseQueryOptions,
    }
  );

  return data;
};

export default useGetTransaction;
