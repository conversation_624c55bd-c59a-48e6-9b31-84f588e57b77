package com.peoplestrust.util.api.common.util;

public class Messages {

  public static String ACCOUNT_WAS_INACTIVE = "Account was Inactive";
  public static String CLIENT_ID_MISMATCH = "Invalid client id";
  public static String PROFILE_WAS_DISABLED = "Profile was not Enabled";
  public static String PROFILE_NOT_FOUND = "Profile not found";
  public static String PROFILE_ID_HEADER_EMPTY = "x-pg-profile-id is empty";
  public static String PROFILE_NOT_FOUND_FOR_CLIENT_ID = "Profile not found for client id";
  public static String ACCOUNT_NOT_FOUND = "Account not found";
  public static String ACCOUNT_ID_HEADER_EMPTY = "x-pg-account-id is empty";
  public static String NO_BALANCES_FOUND = "No balances found for Account";
  public static String INSTRUCTION_NOT_FOUND = "Instruction not found";
  public static String TRANSACTION_NOT_FOUND = "Transaction not found";
  public static String TRANSACTION_METADATA_NOT_FOUND="Transaction metadata not found";
  public static String INVALID_REASON = "Reason must not be blank";
  public static String INSTRUCTION_REF_ID_NOT_FOUND = "Instruction ref id is not found";
  public static String ACCOUNT_WAS_NOT_ACTIVE = "Account status is NOT active, cannot post a transaction";
  public static String PROFILE_WAS_NOT_ENABLED = "Profile status is NOT enabled, cannot post a transaction";
  public static String ACCOUNT_WAS_NOT_ACTIVE_RETRIEVE_TRANSACTION = "Account status is NOT active, cannot retrieve a transaction";
  public static String PROFILE_WAS_NOT_ENABLED_RETRIEVE_TRANSACTION = "Profile status is NOT enabled, cannot retrieve a transaction";
  public static String ACCOUNT_WAS_NOT_ACTIVE_RETRIEVE_INSTRUCTION = "Account status is NOT active, cannot retrieve a instruction";
  public static String PROFILE_WAS_NOT_ENABLED_RETRIEVE_INSTRUCTION = "Profile status is NOT enabled, cannot retrieve a instruction";
  public static String ACCOUNT_WAS_NOT_ACTIVE_ROLLBACK_INSTRUCTION = "Account status is NOT active, cannot rollback a instruction";
  public static String PROFILE_WAS_NOT_ENABLED_ROLLBACK_INSTRUCTION = "Profile status is NOT enabled, cannot rollback a instruction";
  public static String ACCOUNT_WAS_NOT_ACTIVE_COMMIT_INSTRUCTION = "Account status is NOT active, cannot commit a instruction";
  public static String PROFILE_WAS_NOT_ENABLED_COMMIT_INSTRUCTION = "Profile status is NOT enabled, cannot commit a instruction";
  public static String ACCOUNT_WAS_NOT_ACTIVE_REVERSE_TRANSACTION = "Account status is NOT active, cannot reverse a transaction";
  public static String PROFILE_WAS_NOT_ENABLED_REVERSE_TRANSACTION = "Profile status is NOT enabled, cannot reverse a transaction";
  public static String INVALID_INPUT = "Invalid input";
  public static String INVALID_FIELD = "Invalid field";
  public static String RESOURCE_NOT_FOUND = "Resource does not exist";
  public static String TRANSACTION_STATUS_NOT_PENDING = "Transaction status is not PENDING";
  public static String TRANSACTION_STATUS_NOT_POSTED = "Transaction status is not POSTED";
  public static String INSTRUCTION_STATUS_NOT_PENDING = "Instruction status is not PENDING";
  public static String INVALID_PAYMENT_CATEGORY = "Payment category for the transaction was invalid";
  public static String TOO_FEW_QUERY_PARA = "too few query parameter";
  public static String INVALID_PAYMENT_RAIL = "Payment rail for the transaction was invalid";
  public static String START_TIME_NULL = "The startTime is null";
  public static String END_TIME_NULL = "The endTime is null";
  public static String DATE_RANGE_ERROR = "The date range must not exceed 3 days";
  public static String END_TIME_LESS_THAN_START_TIME = "The endTime cannot be less than the startTime";
  public static String NEED_TICKETID_OR_NOTES = "Either ticketId or notes must be provided.";
  public static String NOTES_RANGE = "notes must be between 5 and 256 characters";
  public static String TICKETID_RANGE = "ticketId must be 25 characters or less.";
  public static String INVALID_DATE_TIME = "Invalid Date Time format.";
  public static String INVALID_TICKET_TYPE = "Invalid ticket_type";
  public static String INVALID_TICKET_ID = "Invalid ticket_id";
  public static String INVALID_NOTES = "Invalid notes";
  public static String METADATA_REQUEST_REQUIRED = "Either notes or ticket_type + ticket_id must be provided";
  public static String DUPLICATE_TRANSACTION = "Transaction is already created in GL";
  public static String DUPLICATE_INSTRUCTION = "Instruction is already created in GL";
}
