# tests/test_db.py
import pytest
import types
from batch_job import check_records_in_db
import pandas as pd

class FakeCursor:
    def __init__(self, raise_on_select=False):
        self.raise_on_select = raise_on_select
        self.last_query = None

    def execute(self, sql, params=None):
        self.last_query = sql
        if self.raise_on_select:
            raise RuntimeError("DB error!")

    def fetchall(self):
        return [(1, "ok")]

    def close(self):
        pass

class FakeConn:
    def __init__(self, raise_on_select=False):
        self.cursor_obj = FakeCursor(raise_on_select)

    def cursor(self):
        return self.cursor_obj

def test_check_db_success():
    df = pd.DataFrame({"id": [1]})
    conn = FakeConn()
    # should not raise
    check_records_in_db(df, conn)

def test_check_db_failure():
    df = pd.DataFrame({"id": [1]})
    conn = FakeConn(raise_on_select=True)
    with pytest.raises(RuntimeError):
        check_records_in_db(df, conn)
