

# first stage, extracts the layers from the Java JAR
FROM amazoncorretto:17.0.11-alpine as builder
COPY target/*.jar application.jar
RUN java -jar -Djarmode=layertools application.jar extract

# second stage, copies over the extracted layers
FROM amazoncorretto:17.0.11-alpine

# Metadata
LABEL app.group_id=com.peoplestrust.transaction.scheduler
LABEL app.artifact_id=ledger-rollback-scheduler-api
LABEL app.version=1.0-SNAPSHOT
LABEL app.build_date=2025-03-09T07:50:52-04:00
LABEL app.jre=amazoncorretto-17.0.11-alpine

# Expose application ports (default spring-boot ports)
EXPOSE 8080/tcp

# Expose external volumes

# Application environment variables defaults
#  override with command line when running if different
ENV TZ=America/Toronto
ENV JAVA_OPTS=-Dorg.apache.tomcat.util.buf.UDecoder.ALLOW_ENCODED_SLASH=true -Dorg.apache.catalina.connector.CoyoteAdapter.ALLOW_BACKSLASH=true

# Run application as spring user
RUN addgroup -S spring && adduser -S spring -G spring

WORKDIR /vol/app
COPY --from=builder --chown=spring:spring dependencies/ ./
COPY --from=builder --chown=spring:spring snapshot-dependencies/ ./
COPY --from=builder --chown=spring:spring spring-boot-loader/ ./
COPY --from=builder --chown=spring:spring application/ ./

# Command to execute when container starts
USER spring:spring

ENTRYPOINT exec java $JAVA_OPTS  org.springframework.boot.loader.JarLauncher
