function isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch {
      return false;
    }
  }

const details = [
    {
        title: 'Ticket Type',
        key: (item) => item?.ticket_type || ''
    },
    {
        title: 'Ticket#',
        key: (item) => item?.ticket_id || ''
    },
    {
        title: 'Notes',
        key: (item) => {
            const value = item?.notes
            if (typeof value === 'string' && isValidUrl(value)) {
                return <a href={value} target="_blank" rel="noopener noreferrer">{value}</a>;
            }
            return value || ''
        }
    }

]

export default details
