package com.peoplestrust.qa.transaction.util.api.v1.service;

import static com.peoplestrust.qa.transaction.util.api.v1.util.Messages.CONSUMER_GROUP_NOT_FOUND;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.peoplestrust.qa.transaction.util.api.v1.config.QAUtilProperty;
import com.peoplestrust.util.api.common.exception.ResourceNotFoundException;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AlterConsumerGroupOffsetsResult;
import org.apache.kafka.clients.admin.ConsumerGroupDescription;
import org.apache.kafka.clients.admin.DescribeConsumerGroupsResult;
import org.apache.kafka.clients.admin.ListOffsetsResult;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.KafkaFuture;
import org.apache.kafka.common.TopicPartition;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class KafkaCleanupServiceTest {

  @Mock
  private AdminClient adminClient;

  @Mock
  private QAUtilProperty qaUtilProperty;

  @Spy
  @InjectMocks
  private KafkaCleanupService kafkaCleanupService;

  @Test
  void testCleanupKafkaQueueSuccess()
      throws ExecutionException, InterruptedException, ResourceNotFoundException, TimeoutException {
    String consumerGroupId = "test-consumer-group";
    TopicPartition topicPartition = new TopicPartition("test-topic", 0);
    OffsetAndMetadata currentOffset = new OffsetAndMetadata(50L);
    Map<TopicPartition, OffsetAndMetadata> currentOffsets = Map.of(topicPartition, currentOffset);
    doReturn(currentOffsets).when(kafkaCleanupService)
        .validateConsumerGroupExists(adminClient, consumerGroupId);

    ListOffsetsResult listOffsetsResult = mock(ListOffsetsResult.class);
    KafkaFuture<Map<TopicPartition, ListOffsetsResult.ListOffsetsResultInfo>> futureOffsets = mock(
        KafkaFuture.class);
    Map<TopicPartition, ListOffsetsResult.ListOffsetsResultInfo> latestOffsets = Map.of(
        topicPartition, new ListOffsetsResult.ListOffsetsResultInfo(100L, -1L, null));
    when(adminClient.listOffsets(any())).thenReturn(listOffsetsResult);
    when(listOffsetsResult.all()).thenReturn(futureOffsets);
    when(futureOffsets.get(anyLong(), any())).thenReturn(latestOffsets);

    AlterConsumerGroupOffsetsResult alterConsumerGroupOffsetsResult = mock(
        AlterConsumerGroupOffsetsResult.class);
    KafkaFuture<Void> futureAlterOffsets = mock(KafkaFuture.class);
    when(adminClient.alterConsumerGroupOffsets(eq(consumerGroupId), anyMap())).thenReturn(
        alterConsumerGroupOffsetsResult);
    when(alterConsumerGroupOffsetsResult.all()).thenReturn(futureAlterOffsets);
    when(futureAlterOffsets.get(anyLong(), any())).thenReturn(null);

    kafkaCleanupService.cleanupKafkaQueue(consumerGroupId);
  }

  @Test
  void testCleanupKafkaQueue_exception() throws ExecutionException, InterruptedException {
    String consumerGroupId = "test-consumer-group";
    when(adminClient.describeConsumerGroups(
        eq(Collections.singletonList(consumerGroupId)))).thenThrow(new RuntimeException(
        "Failed to reset offset for partition: test-consumer-group"));
    RuntimeException exception = assertThrows(RuntimeException.class,
        () -> kafkaCleanupService.cleanupKafkaQueue(consumerGroupId));
    assertEquals("Failed to reset offset for partition: test-consumer-group",
        exception.getMessage());
  }

  @Test
  void testCleanupKafkaConsumerGroupNotFound() throws ExecutionException, InterruptedException {
    String consumerGroupId = "no-consumer-group";
    DescribeConsumerGroupsResult describeResult = mock(DescribeConsumerGroupsResult.class);
    when(adminClient.describeConsumerGroups(Collections.singletonList(consumerGroupId))).thenReturn(
        describeResult);
    KafkaFuture<Map<String, ConsumerGroupDescription>> describeFuture = mock(KafkaFuture.class);
    when(describeResult.all()).thenReturn(describeFuture);
    when(describeFuture.get()).thenReturn(Collections.emptyMap());

    ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () ->
        kafkaCleanupService.cleanupKafkaQueue(consumerGroupId));

    assertEquals(consumerGroupId + CONSUMER_GROUP_NOT_FOUND, exception.getMessage());
  }
}