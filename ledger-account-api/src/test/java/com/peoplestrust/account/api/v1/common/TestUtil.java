package com.peoplestrust.account.api.v1.common;

import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.domain.model.AccountStatus;
import com.peoplestrust.account.domain.model.CreateLedgerAccountRequest;
import com.peoplestrust.account.domain.model.Options;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceResponse;
import com.peoplestrust.account.domain.model.UpdateLedgerAccountRequest;
import com.peoplestrust.account.domain.model.UpdateLedgerAccountStatusRequest;
import com.peoplestrust.account.persistence.entity.AccountEntity;
import com.peoplestrust.account.persistence.entity.MonetaryUnit;
import com.peoplestrust.account.persistence.entity.OptionsEntity;
import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import com.peoplestrust.util.api.common.util.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class TestUtil {

  public final static String JWT_TOKEN = "eyJraWQiOiJxeXZnRXIzV0VFT2hONlR1N2hoakg5Q3BkcXp3WGJJY0l1VFdRSCtVRWRnPSIsImFsZyI6IlJTMjU2In0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.O-vUHqyUAwObCijDZx2CGUjye7czV63cYqS1tL_t_sNbVmLS2ghapd75hwAZbDnZ8h8pN1S9JkPkx5qF3r7OticHO0UAyfLUennNN1g713cYTD_pIVqXXd7WUowyvstLhaVpNwhpqTID5vuu8f3J_yHImjUBUtYGvnR4uV7WnpWEVL2LsbhCa0iB_LeDBZEws7OYBvPwqOCaf7y58EMukOUtwizMEAslEofF-5K9RXG_Q8TKApcKixnheMqDZ7IcbKlK9XLMqn-sr147BvvaPRH1DmQbrPxFSz1z6Ihw_wAI21qDKXWX-OxGv_-gYq9NF-a-HpO0qHo_GB4NDh31EA";
  public final static String BAD_JWT_TOKEN = "eyJraWQiOiJxeXZnRXIzV0VFT2hONlR1N2hoakg5Q3BkcXp3WGJJY0l1VFdRSCtVRWRnPSIsImFsZyI6IlJTMjU2In0.eyJzdWIiOiJjZTlhOWQ0ZC1jMWNmLTQxNWQtOTllNS1mNzg4MzZiYzFhOTIiLCJjb2duaXRvOmdyb3VwcyI6WyJHTF9BRE1JTklTVFJBVElPTiIsIkVGVF9ET0NVTUVOVEFUSU9OIiwidXMtZWFzdC0yX29IMkI1UUlVb19QZW9wbGVzVHJ1c3RBREZTIiwiRVRSQU5TRkVSX0FETUlOSVNUUkFUT1IiXSwiaXNzIjoiaHR0cHM6XC9cL2NvZ25pdG8taWRwLnVzLWVhc3QtMi5hbWF6b25hd3MuY29tXC91cy1lYXN0LTJfb0gyQjVRSVVvIiwidmVyc2lvbiI6MiwiY2xpZW50X2lkIjoiMjAxdGxhMHZhdXE3cmk2Zmx2MGpuYWIyZ2UiLCJvcmlnaW5fanRpIjoiOGUzMmFjM2YtMTQ1OS00ZmE0LWEwZWMtZjk4NGY4YzY2MmJhIiwidG9rZW5fdXNlIjoiYWNjZXNzIiwic2NvcGUiOiJvcGVuaWQgY29tLnBlb3BsZXNncm91cC5sZWRnZXIuc3RnXC9hZG1pblVJLmFsbCBlbWFpbCIsImF1dGhfdGltZSI6MTcyMjI4NDE1MywiZXhwIjoxNzIyMjg3NzUzLCJpYXQiOjE3MjIyODQxNTMsImp0aSI6IjU1YTcyNTM5LWM0MzUtNDJjMi1hODhhLWZkMTA3MTdiNDNlZiIsI.O-vUHqyUAwObCijDZx2CGUjye7czV63cYqS1tL_t_sNbVmLS2ghapd75hwAZbDnZ8h8pN1S9JkPkx5qF3r7OticHO0UAyfLUennNN1g713cYTD_pIVqXXd7WUowyvstLhaVpNwhpqTID5vuu8f3J_yHImjUBUtYGvnR4uV7WnpWEVL2LsbhCa0iB_LeDBZEws7OYBvPwqOCaf7y58EMukOUtwizMEAslEofF-5K9RXG_Q8TKApcKixnheMqDZ7IcbKlK9XLMqn-sr147BvvaPRH1DmQbrPxFSz1z6Ihw_wAI21qDKXWX-OxGv_-gYq9NF-a-HpO0qHo_GB4NDh31EA";

  public static CreateLedgerAccountRequest createAccountPostRequest() {
    CreateLedgerAccountRequest req = new CreateLedgerAccountRequest();
    req.setName("TEST-NAME");
    req.setMonetaryUnit("CAD");
    req.setDescription("TEST-DESCRIPTION");
    Options op = new Options(BigDecimal.valueOf(121.09), "5");
    req.setOptions(op);
    return req;
  }

  public static UpdateLedgerAccountRequest createAccountPutRequest() {
    UpdateLedgerAccountRequest req = new UpdateLedgerAccountRequest();
    req.setName("TEST-NAME");
    req.setMonetaryUnit("CAD");
    req.setDescription("TEST-DESCRIPTION");
    Options op = new Options(BigDecimal.valueOf(121.09), "5");
    req.setOptions(op);
    return req;
  }

  public static CreateLedgerAccountRequest createSPCAccountPostRequest() {
    CreateLedgerAccountRequest req = new CreateLedgerAccountRequest();
    req.setName("TEST-NAME-àâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ");
    req.setMonetaryUnit("CAD");
    req.setDescription("TEST-DESCRIPTION-àâäèéêëîïôœùûüÿçÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇ");
    Options op = new Options(BigDecimal.valueOf(121.09), "5");
    req.setOptions(op);
    return req;
  }

  public static UpdateLedgerAccountStatusRequest createUpdateLedgerAccountStatusRequest() {
    UpdateLedgerAccountStatusRequest req = new UpdateLedgerAccountStatusRequest();
    req.reason("TEST");
    req.setStatus(AccountStatus.INACTIVE);
    return req;
  }

  public static Account createAccount(String profileId) throws Exception {
    UUID uuid = UUID.randomUUID();
    com.peoplestrust.account.api.v1.model.Options options =
        new com.peoplestrust.account.api.v1.model.Options();
    options.setFundHoldDays(4);
    options.setOverdraftAmount(BigDecimal.valueOf(121.09));

    Account account =
        Account.builder()
            .refId(uuid.toString())
            .status(com.peoplestrust.account.persistence.entity.AccountStatus.ACTIVE)
            .monetaryUnit("CAD")
            .name("TEST-NAME")
            .options(options)
            .description("TEST_DESCRIPTION")
            .createdDateTime(DateUtils.offset())
            .updatedDateTime(DateUtils.offset())
            .profileId(profileId)
            .build();
    AccountEntity accountEntity = getAccountData(account, profileId);
    return account;
  }

  public static AccountEntity getAccountData(Account account, String profileId) {
    AccountEntity accountEntity = new AccountEntity();
    OptionsEntity optionsEntity = new OptionsEntity();
    optionsEntity.setFundHoldDays(account.getOptions().getFundHoldDays());
    optionsEntity.setOverdraftAmount(account.getOptions().getOverdraftAmount());
    accountEntity.setCreatedDateTime(DateUtils.offset());
    accountEntity.setUpdatedDateTime(DateUtils.offset());
    accountEntity.setName(account.getName());
    accountEntity.setOptions(optionsEntity);
    accountEntity.setRefId(UUID.fromString(account.getRefId()));
    accountEntity.setDescription(account.getDescription());
    MonetaryUnit monetaryUnit = MonetaryUnit.valueOf(account.getMonetaryUnit());
    accountEntity.setMonetaryUnit(monetaryUnit);
    accountEntity.setStatus(com.peoplestrust.account.persistence.entity.AccountStatus.ACTIVE);
    accountEntity.setProfileId(UUID.fromString(profileId));
    return accountEntity;
  }

  public static List<AccountEntity> getAccountEntityLst(Account account, String profileId, int number) {
    List<AccountEntity> accountEntities = new ArrayList<>();
    for (int i = 0; i < number; i++) {
      AccountEntity accountEntity = getAccountData(account, profileId);
      accountEntities.add(accountEntity);
    }
    return accountEntities;
  }

  public static RetrieveLedgerAccountBalanceResponse getBalanceResponse() {
    return RetrieveLedgerAccountBalanceResponse.builder().
        accountBalance(BigDecimal.valueOf(200))
        .availableBalance(BigDecimal.valueOf(1000))
        .effectiveOn(DateUtils.offsetDateTime())
        .fundHoldAmount(BigDecimal.valueOf(200))
        .prefundReserveAmount(BigDecimal.valueOf(400))
        .overdraftAmount(BigDecimal.valueOf(600)).build();
  }

  public static List<BalanceSnapshotResponse> get14DaysBalanceResponse() {
    BalanceSnapshotResponse b1 = new BalanceSnapshotResponse();
    List<BalanceSnapshotResponse> balance14Days = new ArrayList<>();
    balance14Days.add(b1);
    return balance14Days;
  }

  public static List<BalanceSnapshotResponse> getBalanceSnapshotResponseList() {
    BalanceSnapshotResponse b1 = new BalanceSnapshotResponse();
    b1.setTotalAmountCredit(BigDecimal.valueOf(200));
    b1.setTotalAmountDebit(BigDecimal.valueOf(50));
    b1.setTotalReserveAmount(BigDecimal.valueOf(50));
    b1.setTotalAmount(BigDecimal.valueOf(200 - 50 + 50));
    b1.setCreatedDateTime(OffsetDateTime.now());
    b1.setEffectiveFromDateTime(LocalDateTime.now().minusDays(1).atOffset(ZoneOffset.UTC));
    b1.setEffectiveToDateTime(LocalDateTime.now().atOffset(ZoneOffset.UTC));
    List<BalanceSnapshotResponse> balanceList = new ArrayList<>();
    balanceList.add(b1);
    return balanceList;
  }

  public static List<BalanceSnapshotResponse> get14DaysBalanceResponse(Account account) {
    BalanceSnapshotResponse b1 = new BalanceSnapshotResponse();
    BigDecimal credit = BigDecimal.valueOf(200);
    BigDecimal debit = BigDecimal.valueOf(50);
    BigDecimal reserve = BigDecimal.valueOf(50);
    b1.setTotalAmountDebit(debit);
    b1.setTotalReserveAmount(reserve);
    b1.setTotalAmountCredit(credit);
    b1.setTotalAmount(credit.subtract(debit).add(reserve));
    b1.setCreatedDateTime(OffsetDateTime.now());
    b1.setEffectiveFromDateTime(DateUtils.startOfDay(1).atOffset(ZoneOffset.UTC));
    b1.setEffectiveToDateTime(DateUtils.startOfDay(0).atOffset(ZoneOffset.UTC));
    List<BalanceSnapshotResponse> balance14Days = new ArrayList<>();
    balance14Days.add(b1);

    return balance14Days;
  }

  public static Account createUpdateAccount() {
    Account account =
        Account.builder()
            .name("TEST-NAME")
            .monetaryUnit("CAD")
            .description("TEST-DESCRIPTION")
            .build();
    return account;
  }

  public static Account createAccount() {
    com.peoplestrust.account.api.v1.model.Options op = new com.peoplestrust.account.api.v1.model.Options();
    op.setFundHoldDays(4);
    op.setOverdraftAmount(BigDecimal.valueOf(121.09));

    Account account =
        Account.builder()
            .refId(UUID.randomUUID().toString())
            .status(com.peoplestrust.account.persistence.entity.AccountStatus.INACTIVE)
            .monetaryUnit("CAD")
            .name("TEST-NAME")
            .options(op)
            .description("TEST_DESCRIPTION")
            .createdDateTime(DateUtils.offset())
            .updatedDateTime(DateUtils.offset())
            .build();
    AccountEntity accountEntity = getAccountData(account, UUID.randomUUID().toString());
    return account;
  }


}
