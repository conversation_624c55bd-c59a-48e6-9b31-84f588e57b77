import generateDate from "utils/generateDate"
import amountFormat from "utils/amountFormat"

const details = (props) => [
    {
        title: 'Account Name',
        key: 'name'
    },
    {
        title: 'CRM ID',
        key: (item) => props?.crm_id
    },
    {
        title: 'Reference ID',
        key: 'ref_Id'
    },
    {
        title: 'Description',
        key: 'description'
    },
    {
        title: 'Currency',
        key: 'monetary_unit'
    },
    {
        title: 'Overdraft Amount',
        key: (item) => amountFormat(item?.options?.overdraft_amount)
    },
    {
        title: 'Fund Hold days',
        key: 'options.fund_hold_days'
    },
    {
        title: 'Account Status',
        key: 'status'
    },
    {
        title: 'Created Date',
        key: (item) => generateDate(item?.created_date_time) || 'N/A'
    },
    {
        title: 'Updated Date',
        key: (item) => generateDate(item?.updated_date_time) || 'N/A'
    }
]

export default details