import { useState, useEffect } from 'react' 
import SubMenu from './SubMenu'
import { useLocation } from 'react-router-dom'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import useCurrentPath from 'hooks/useCurrentPath'
import { faPlus, faMinus } from '@fortawesome/free-solid-svg-icons'
import * as S from './styled'

const MenuItem = ({ route, testId }) => {
    const location = useLocation()
    const currentPath = useCurrentPath()
    const [open, setOpen] = useState(false)

    const onOpen = (e) => {
        e.preventDefault()
        setOpen(!open)
    }

    useEffect(() => {
        // if(currentPath?.path === route.path) {
        //     setOpen(!!route.submenu)
        // }
    }, [route])

    return (
        <S.MenuItem 
            data-testid={ testId }
            className={`${open ? 'open' : ''}`} 
        >
            <S.LinkContianer>
                <S.MenuLink to={route.to} className={`${location.pathname === route.to ? 'active' : ''}`}>
                    {route.name}
                    {
                        route.submenu && (
                            <FontAwesomeIcon icon={open ? faMinus : faPlus} onClick={onOpen}/>
                        )
                    }
                </S.MenuLink>
            </S.LinkContianer>
            <SubMenu data={route.submenu} currentPath={currentPath?.path} setOpen={setOpen} />
        </S.MenuItem>
    )
}

export default MenuItem