"""
tests/conftest.py
─────────────────
Stubs psycopg2, datadog, python-dotenv and provides S3 / time fixtures.
Compatible with Moto 4.x (`mock_s3`) and Moto 5.x (`mock_aws`).
"""
import sys, types, os, pytest
from freezegun import freeze_time

# ---------------------------------------------------------------------
# 0️⃣  Set core env-vars *before* batch_job import happens -------------
# ---------------------------------------------------------------------
os.environ.setdefault("ENV",         "staging")
os.environ.setdefault("DOMAIN",      "unit-tests")
os.environ.setdefault("SERVICE",     "batch-job")
os.environ.setdefault("VERSION",     "0.0.0")
os.environ.setdefault("S3_BUCKET",   "my-bucket")
os.environ.setdefault("AWS_REGION",  "us-east-1")
os.environ.setdefault("S3_INPUT_PREFIX",   "input/")
os.environ.setdefault("S3_ARCHIVE_PREFIX", "archive/")
os.environ.setdefault("DD_DOGSTATSD_URL",  "/var/run/datadog/dsd.socket")

# ---------------------------------------------------------------------
# 1️⃣  Stubs for third-party libs --------------------------------------
# ---------------------------------------------------------------------
psycopg2_stub = types.ModuleType("psycopg2")
class _FakeCursor:
    def execute(self, *a, **k): pass
    def fetchall(self): return [(1,)]
    def fetchone(self): return (1,)
    def close(self):    pass
class _FakeConn:
    def cursor(self): return _FakeCursor()
    def close(self):  pass
psycopg2_stub.connect = lambda **_: _FakeConn()
sys.modules["psycopg2"] = psycopg2_stub

datadog_stub = types.ModuleType("datadog")
datadog_stub.initialize = lambda **_: None
datadog_stub.statsd = types.SimpleNamespace(
    increment=lambda *a, **k: None,
    gauge    =lambda *a, **k: None,
    timing   =lambda *a, **k: None,
    event    =lambda *a, **k: None,
)
sys.modules["datadog"] = datadog_stub

dotenv_stub = types.ModuleType("dotenv")
dotenv_stub.load_dotenv = lambda *a, **k: None
sys.modules["dotenv"] = dotenv_stub
# ---------------------------------------------------------------------

# ---------------------------------------------------------------------
# 2️⃣  Moto import (4.x vs 5.x) ----------------------------------------
# ---------------------------------------------------------------------
try:
    from moto import mock_s3            # Moto < 5
    _S3_CTX = mock_s3
except ImportError:
    from moto import mock_aws           # Moto ≥ 5
    _S3_CTX = mock_aws                  # signature now takes no kwargs
# ---------------------------------------------------------------------

import boto3

# -- baseline env vars fixture (still handy to override in tests) ------
@pytest.fixture(autouse=True)
def env_vars(monkeypatch):
    # everything is already set above, but fixture kept for overrides
    yield

# -- freeze deterministic timestamp -----------------------------------
@freeze_time("2025-05-20 12:00:00")
@pytest.fixture
def frozen_time():
    yield

# -- S3 sandbox --------------------------------------------------------
@pytest.fixture
def s3_bucket():
    with _S3_CTX():
        s3 = boto3.client(
            "s3",
            region_name=os.environ["AWS_REGION"],
        )
        s3.create_bucket(Bucket=os.environ["S3_BUCKET"])

        s3.put_object(Bucket=os.environ["S3_BUCKET"], Key="input/file1.csv", Body="a,b\n1,2")
        s3.put_object(Bucket=os.environ["S3_BUCKET"], Key="input/file2.csv", Body="x,y\n3,4")
        s3.put_object(Bucket=os.environ["S3_BUCKET"], Key="input/readme.txt", Body="ignore")
        yield s3
