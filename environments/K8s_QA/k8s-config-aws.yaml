apiVersion: v1
kind: Secret
metadata:
  name: gl-recon-aws
  namespace: pg-ledger-qa
type: Opaque
stringData:
  sys.pg.gl.aws.key: "********************"
  sys.pg.gl.aws.secret: "OZDAwTAItvS+jLLG41rcP6xeO2BsY4LdDDSrT3dj"
  sys.pg.gl.aws.bucket: "pg-gl-recon-qa"
  sys.pg.gl.aws.input.prefix: "input"
  sys.pg.gl.aws.archive.prefix: "archive"
  sys.pg.gl.aws.failed.prefix: "failed"
  sys.pg.gl.aws.region: "ca-central-1"
  sys.pg.gl.aws.s3.poll.interval: "30"