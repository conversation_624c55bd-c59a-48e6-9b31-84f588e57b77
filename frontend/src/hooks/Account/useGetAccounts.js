import {
    useQuery
} from 'react-query'
import baseQueryOptions from 'constants/baseQueryOptions'
import { getAccountsService } from 'services/account'

const useGetAccount = (id) => {
    const { data } = useQuery(['getAccount', id], () => getAccountsService(id), {
        keepPreviousData: true,
        enabled: !!id,
        ...baseQueryOptions
    })

    return data

}

export default useGetAccount