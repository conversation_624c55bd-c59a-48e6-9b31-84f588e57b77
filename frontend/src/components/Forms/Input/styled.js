import styled from "styled-components";
import TextField from "@mui/material/TextField";
import { TextareaAutosize } from "@mui/base/TextareaAutosize";
import FormControlLabel from "@mui/material/FormControlLabel";

export const Input = styled(TextField)`
  position: relative;
  .css-14s5rfu-MuiFormLabel-root-MuiInputLabel-root {
    font-size: 13px;
    color: #212121;
  }

  .MuiInputBase-root {
    padding: 1.5px;
    .MuiOutlinedInput-input {
      font-size: 13px;
      color: #212121;
      padding: 10px;

      &::placeholder {
        color: black;
        opacity: 0.6;
      }
    }

    .MuiInputAdornment-root {
      button {
        margin: 0px;
      }
    }
  }

  &.has-error {
  }

  .css-1wc848c-MuiFormHelperText-root.Mui-error {
    position: absolute;
    bottom: -15px;
  }
`;

export const TextareaInput = styled(TextareaAutosize).attrs({
  rows: 10, // Fixed number of rows
})`
  width: 100%;
  padding: 10px;
  font-size: 13px;
  color: #212121;
  border: 1px solid #ced4da;
  border-radius: 4px;
  &:focus {
    border-color: #86b7fe;
    outline: none;
  }
  &::placeholder {
    color: black;
    opacity: 0.6;
  }
`;

export const SearchInput = styled.div`
  .input-group-text {
    border-radius: 100%;
    box-shadow: unset;
    border: none;
    color: white;
    background-color: rgba(255, 58, 45, 0.59);
  }

  input {
    padding: 5px;
  }
`;

export const Checkbox = styled(FormControlLabel)`
  justify-content: flex-start;
  flex-direction: row !important;

  .MuiFormControlLabel-label {
    order: 1;
  }

  .MuiCheckbox-root {
    order: 2;
  }
`;

export const FromToInputWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 10px;
`;

export const DatePicker = styled.div`
  .form-group {
    display: flex;
    flex-direction: column;
  }

  .react-datepicker-wrapper {
    input {
      width: 100%;
      box-shadow: unset !important;
      outline: none;
      padding: 10px;
      border-radius: 25px;
      font-size: 13px;
      color: #212121;
      border: 1px solid #ced4da;
      transition: border-color 0.15s ease-in-out;

      &:focus {
        border-color: #86b7fe;
      }
    }
  }
`;
