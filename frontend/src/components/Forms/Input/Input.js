import React, { useMemo } from "react";
import SelectInput from "../Select";
import DatePickerInput from "./DatePickerInput";
import Search from "./Search";
import Checkbox from "./Checkbox";
import YesOrNo from "./YesOrNo";
import TextareaInput from "./TextareaInput";
import * as _ from "lodash";
import * as S from "./styled";

const StaticText = (props) => {
  return (
    <p className="static-text">
      <span>{props.title}</span>
      <b>{props.text}</b>
    </p>
  );
};

const types = {
  search: Search,
  date: DatePickerInput,
  select: SelectInput,
  checkbox: Checkbox,
  yesorno: YesOrNo,
  statictext: StaticText,
  textarea: TextareaInput,
  default: S.Input,
};

const blacklists = {
  select: ["label", "error"],
  yesorno: ["label", "error"],
  checkbox: ["error", "setValues"],
  date: ["label", "error"],
  textarea: ["error"],
  default: ["setValues", "label", "error", "values", "setFieldValue"],
};

const Input = (props) => {
  const Component = useMemo(() => {
    return types[props.type] || types.default;
  }, [props.type]);

  const blackList = useMemo(() => {
    return blacklists[props.type] || blacklists.default;
  }, [props.type]);

  // Pass maxLength and other native input attributes to inputProps
  const inputProps = props.maxLength ? { maxLength: props.maxLength } : {};

  return (
    <Component
      variant="outlined"
      placeholder={props.label}
      error={!!props.error}
      inputProps={inputProps}
      className={`${!!props.error ? "has-error" : ""} ${
        props.type || "default"
      }-input`}
      helperText={props.error}
      {..._.omit(props, blackList)}
      data-testid={`${types[props.type] ? props.type : "default"}-input`}
    />
  );
};

export default Input;
