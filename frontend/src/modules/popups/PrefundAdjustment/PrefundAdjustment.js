import Form from 'components/Forms/Form'
import prefundAdjustmentForm from 'constants/forms/prefundAdjustment'
import { useModal } from 'contexts/ModalProvider'
import * as S from '../styled'

const PrefundAdjustment = (props) => {
    const { show, closeModal, formik } = useModal()

    return (
        <S.Modal
            show={show}
            handleClose={closeModal}
            onCreate={formik.handleSubmit}
            title='Prefund Adjustment'
            createTxt='Save Changes'
            testId={props.testId || 'prefund-adjustment-modal'}
        >
            <Form
                formData={prefundAdjustmentForm}
                formik={formik}
                data-testid='form'
                noButton
            />
        </S.Modal>
    )
}


export default PrefundAdjustment
