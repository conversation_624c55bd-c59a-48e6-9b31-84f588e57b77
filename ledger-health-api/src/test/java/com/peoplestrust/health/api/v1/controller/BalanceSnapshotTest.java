package com.peoplestrust.health.api.v1.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.peoplestrust.health.api.v1.HealthTestUtil;
import com.peoplestrust.health.api.v1.service.HealthService;
import com.peoplestrust.health.domain.model.HealthResponse;
import com.peoplestrust.scheduler.persistence.repository.read.ReadBalanceRepository;
import com.peoplestrust.util.api.common.config.APICommonUtilConstant;
import java.time.Instant;
import java.util.Objects;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

@Slf4j
@ExtendWith(MockitoExtension.class)
@SpringBootTest
@AutoConfigureMockMvc
public class BalanceSnapshotTest {

  private static final String URL = "/v1/ledger/health/balance/snapshot";

  @Autowired
  HealthService healthService;
  @MockBean
  ReadBalanceRepository readBalanceRepository;
  @Autowired
  private MockMvc mockMvc;
  private HttpHeaders headers;

  private HealthResponse healthResponse;

  private ObjectMapper objectMapper;

  @BeforeEach
  public void setupBeforeTest() {
    UUID interactionId = UUID.randomUUID();
    headers = new HttpHeaders();
    headers.add(APICommonUtilConstant.AUTHORIZATION, HealthTestUtil.JWT_TOKEN);
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_ID, interactionId.toString());
    headers.add(APICommonUtilConstant.HEADER_INTERACTION_TIMESTAMP, Instant.now().toString());
  }

  @Test
  public void balanceSnapshot_success() throws Exception {
    when(readBalanceRepository.countSnapshotsByDateTime(any(), any())).thenReturn(1L);
    MvcResult result =
        mockMvc
            .perform(MockMvcRequestBuilders.get(URL).headers(headers))
            .andExpect(status().isOk())
            .andReturn();

    objectMapper = new ObjectMapper();
    JsonNode jsonNode =
        objectMapper.readTree(Objects.requireNonNull(result.getResponse().getContentAsString()));
    assertEquals("SUCCESS", jsonNode.get("status").asText());
  }

  @Test
  public void balanceSnapshot_BadRequest() throws Exception {
    headers.remove(APICommonUtilConstant.HEADER_INTERACTION_ID);
    when(readBalanceRepository.countSnapshotsByDateTime(any(), any())).thenReturn(1L);
    mockMvc
        .perform(MockMvcRequestBuilders.get(URL).headers(headers))
        .andExpect(status().isBadRequest())
        .andReturn();
  }

  @Test
  public void balanceSnapshot_NoAuth_BadRequest() throws Exception {
    //Remove the token trigger 400 error
    headers.remove(APICommonUtilConstant.AUTHORIZATION);
    when(readBalanceRepository.countSnapshotsByDateTime(any(), any())).thenReturn(1L);
    mockMvc
            .perform(MockMvcRequestBuilders.get(URL).headers(headers))
            .andExpect(status().isBadRequest())
            .andReturn();
  }

  @Test
  public void balanceSnapshot_BadAuth_401Error() throws Exception {
    //set bad token trigger 401 error
    headers.set(APICommonUtilConstant.AUTHORIZATION, HealthTestUtil.BAD_JWT_TOKEN);
    when(readBalanceRepository.countSnapshotsByDateTime(any(), any())).thenReturn(1L);
    mockMvc
            .perform(MockMvcRequestBuilders.get(URL).headers(headers))
            .andExpect(status().isUnauthorized())
            .andReturn();
  }
}