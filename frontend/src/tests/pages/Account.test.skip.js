import { render, act, fireEvent } from '@testing-library/react'
import Account from 'pages/Account'
import { QueryClientProvider } from 'react-query'
import queryClient from 'services/client/queryClient'
import findByTestId from 'utils/findByTestId'

const setUp = (props) => {
    render(
        <QueryClientProvider client={queryClient}>
            <Account {...props} />
        </QueryClientProvider>
    )
}

describe('Account page deep render', () => {

    it('Should render page correctly', async () => {
        await act(() => {
            setUp({})
        })
        const container = findByTestId('page-container')
        expect(container).toBeInTheDocument()
    })

    it('Should render page title correctly', async () => {
        await act(() => {
            setUp({})
        })
        const title = findByTestId('page-title')
        expect(title).toBeInTheDocument()
        expect(title).toHaveTextContent('Test Account')
    })

    it('Should render update button', async () => {
        await act(() => {
            setUp({})
        })
        const button = findByTestId('update-button')
        expect(button).toBeInTheDocument()
        expect(button).toHaveTextContent('Edit Account')
    })

    it('Update button trigger should render modal component', async () => {
        await act(() => {
            setUp({})
        })
        const button = findByTestId('update-button')
        fireEvent(button, new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
        }))

        const modal = findByTestId('update-account-modal')
        expect(modal).toBeInTheDocument()
    })

    
    it('Should render manual deposit funds button', async () => {
        await act(() => {
            setUp({})
        })
        const button = findByTestId('deposit-funds-button')
        expect(button).toBeInTheDocument()
        expect(button).toHaveTextContent('Deposit/Withdraw Funds')
    })

    it('Deposit funds button trigger should render modal component', async () => {
        await act(() => {
            setUp({})
        })
        const button = findByTestId('deposit-funds-button')

        fireEvent(button, new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
        }))

        const modal = findByTestId('prefund-adjustment-modal')
        expect(modal).toBeInTheDocument()
    })

    
    it('Should render manual adjustment button', async () => {
        await act(() => {
            setUp({})
        })
        const button = findByTestId('manul-adjustment-button')
        expect(button).toBeInTheDocument()
        expect(button).toHaveTextContent('Manual Adjustment')
    })

    it('Adjustment button trigger should render modal component', async () => {
        await act(() => {
            setUp({})
        })
        const button = findByTestId('manul-adjustment-button')

        fireEvent(button, new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
        }))

        const modal = findByTestId('manual-adjustment-modal')
        expect(modal).toBeInTheDocument()
    })


    describe('Table component', () => {
        it('Should render page table correctly', async () => {
            await act(() => {
                setUp({})
            })
            const table = findByTestId('table-container')
            expect(table).toBeInTheDocument()
        })
    })

    describe('Details component', () => {
        it('Should render component correctly', async () => {
            await act(() => {
                setUp({})
            })

            const details = findByTestId('details')
            
            expect(details).toBeInTheDocument()

        })
    })

})