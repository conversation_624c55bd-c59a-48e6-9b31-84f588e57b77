// Modal.test.js
import { render } from '@testing-library/react';
import Modal from 'components/Modal';
import findByTestId from 'utils/findByTestId';

const setUp = (props) => {
    render(<Modal {...props} />);
};

describe('Modal component', () => {
    it('should not render when show is false', () => {
        setUp({ show: false });
        const modal = findByTestId('modal', 'query');
        expect(modal).not.toBeInTheDocument();
    });

    it('should render when show is true', () => {
        setUp({ show: true });
        const modal = findByTestId('modal');
        expect(modal).toBeInTheDocument();
    });

    it('Should not render button if onCreate props is not provided', () => {
        setUp({ show: true });
        const button = findByTestId('create-button', 'query');
        expect(button).not.toBeInTheDocument();
    });

    it('Should render button if onCreate props is provided', () => {
        setUp({ show: true, onCreate: () => {} });
        const button = findByTestId('create-button');
        expect(button).toBeInTheDocument();
        expect(button).toHaveTextContent('Create');
    });

    it('Should render create button with custom text', () => {
        setUp({
            show: true,
            onCreate: () => {},
            createTxt: 'Custom Text'
        });
        const button = findByTestId('create-button');
        expect(button).toHaveTextContent('Custom Text');
    });

    // UPDATED TEST — now checks for 'CloseIcon' instead of 'close-button'
    it('Should render close button', () => {
        setUp({ show: true });
        // The modal contains an <svg data-testid="CloseIcon" ... />
        // so let's check for that instead
        const closeIcon = findByTestId('CloseIcon');
        expect(closeIcon).toBeInTheDocument();
    });

    it('Should render title with props => Test Title', () => {
        setUp({
            show: true,
            title: 'Test Title'
        });
        const title = findByTestId('modal-title');
        expect(title).toBeInTheDocument();
        expect(title).toHaveTextContent('Test Title');
    });
});
