import React, { useMemo } from 'react'
import Form from '../Forms/Form'
import { useFormik } from 'formik'
import qs from 'qs'
import { useLocation } from 'react-router-dom'
import * as S from './styled'

const getSearchObj = (location, filters) => {
    const parsed = location?.search?.replace('?', '') 
    const search = qs.parse(parsed) 
    const initials = filters?.reduce((nexFilter, filter) => {
        return {
            ...nexFilter,
            [filter.name]: ''
        }
    }, {})

    if (Object.keys(search).length) {
        return {
            ...initials || {},
            ...search
        }
    }
    if (filters) {
        return initials
    }
    return {}
}

const TableFilter = (props) => {
    const location = useLocation()
    const formik = useFormik({
        initialValues: useMemo(() => {
            return props?.filters?.reduce((nextFilter, filter) => {
                return {
                    ...nextFilter,
                    [filter.name]: filter.value || ''
                }
            }, {})
        }, [props.filters]),
        onSubmit: (data) => {
            const search = getSearchObj(location)
            props.onFilter({
                ...search,
                ...data,
            })
        }
    }) // formik configuration

    return (
        <S.TableFilter data-testid='table-filter'>
            <Form
                {...props.filterForms}
                formData={props.filters}
                formik={formik}
                buttonText='Search'
                buttonDisabled={props.buttonDisabled}
                withOnChange={props.withOnChange}
            />
        </S.TableFilter>
    )

}

export default TableFilter