import Form from "components/Forms/Form";
import CreateAccountForm from "constants/forms/createAccount";
import UpdateACcountForm from "constants/forms/updateAccount";
import { useModal } from "contexts/ModalProvider";
import * as S from "../styled";

const CreateAccount = (props) => {
  const { show, closeModal, formik } = useModal();

  const currentForm = props.edit ? UpdateACcountForm : CreateAccountForm;

  const staticText = {
    type: "statictext",
    title: "User Reference ID: ",
    text: props?.mutationData?.ref_id,
  };

  const changedAccountForm = props?.mutationData?.ref_id
    ? [...currentForm, staticText]
    : currentForm;

  return (
    <S.Modal
      show={show}
      handleClose={closeModal}
      onCreate={formik.handleSubmit}
      title={props.edit ? "Update Account" : "Create New Account"}
      createTxt={props.edit ? "Update Account" : "Create Account"}
      testId={props.testId || "create-account-modal"}
    >
      <Form
        formData={changedAccountForm}
        formik={formik}
        data-testid="form"
        noButton
      />
    </S.Modal>
  );
};

export default CreateAccount;
