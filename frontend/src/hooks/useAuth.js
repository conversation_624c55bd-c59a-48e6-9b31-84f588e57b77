import { useState } from "react";
import cognitoAuth from "../services/client/cognito";
import { decode } from "jsonwebtoken";

const getSessionProps = () => {
  if (!cognitoAuth.isUserSignedIn()) return null;
  const { signInUserSession: { idToken: { jwtToken: jwtIdToken } = {} } = {} } =
    cognitoAuth || {};
  const {
    "cognito:username": username = [],
    "cognito:groups": cognitoGroups,
    given_name, // eslint-disable-line
    family_name, // eslint-disable-line
    email,
  } = decode(jwtIdToken) || {};

  return {
    username,
    firstName: given_name,
    lastName: family_name,
    email,
    cognitoGroups,
  };
};

const isAllowed = (userGroup) => {
  if (!userGroup) return true;
  const cognitoUserGroups = getSessionProps()?.cognitoGroups;
  if (!cognitoUserGroups) return false;
  if (typeof userGroup === "string") {
    return cognitoUserGroups.indexOf(userGroup) !== -1;
  }
  let allowed = false;
  const size = userGroup.length;
  let i = 0;
  while (!allowed && i < size) {
    allowed = cognitoUserGroups.indexOf(userGroup[i]) !== -1;
    i++;
  }
  return allowed;
};

const login = () => {
  const url = cognitoAuth.getFQDNSignIn();
  window.location.assign(url);
};

export const logout = () => {
  cognitoAuth.signInUserSession = null;
  cognitoAuth.clearCachedTokensScopes();
  cognitoAuth.signOut();
  window.history.replaceState("", document.title, "/");
  window.location.reload(true);
};

const useAuth = () => {
  const [state, setState] = useState({
    isSignedIn: cognitoAuth.isUserSignedIn() || false,
    isAllowed,
    user: null,
    login,
    logout,
  });

  const userSessionUpdate = () => {
    setState({
      ...state,
      isSignedIn: cognitoAuth.isUserSignedIn(),
      user: getSessionProps(),
    });
  };

  return {
    ...state,
    userSessionUpdate,
  };
};

export default useAuth;
