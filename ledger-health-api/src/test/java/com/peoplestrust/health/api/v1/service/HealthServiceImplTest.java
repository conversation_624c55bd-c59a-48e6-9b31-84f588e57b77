package com.peoplestrust.health.api.v1.service;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.peoplestrust.health.api.v1.config.HealthProperty;
import com.peoplestrust.util.api.common.exception.ValidationException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;


@ExtendWith(MockitoExtension.class)
@SpringBootTest
@Slf4j
public class HealthServiceImplTest {

  @Mock
  private HealthProperty healthProperty;

  @MockBean
  private CloudWatchLogQuery cloudWatchLogQuery;

  @InjectMocks
  private HealthServiceImpl healthService;


  @Test
  public void testGetHttpInternalErrorWithDateRange_Success() throws Exception {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 25, 10, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    when(healthProperty.getEksNameSpace()).thenReturn("pg-ledger");
    when(healthProperty.getCloudWatchLogGroup()).thenReturn("/non-prod/general-ledger/");
    Map<String, String> map = Collections.singletonMap("totalInternal_ErrorCount", "1");
    JSONArray jsonArray = new JSONArray();
    jsonArray.put(map);
    when(cloudWatchLogQuery.executeQuery(anyString(), anyLong(), anyLong(), anyString())).thenReturn(jsonArray.toString());
    String result = healthService.getHttpInternalErrorWithDateRange(startTime, endTime);
    assertNotNull(result);

  }

  @Test
  public void testGetHttpInternalErrorWithDateRange_Failure() {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 20, 10, 18);
    assertThrows(ValidationException.class, () -> healthService.getHttpInternalErrorWithDateRange(startTime, endTime));

  }

  //Timeout_Error

  @Test
  public void testGetTimeOutErrorWithDateRange_Success() throws Exception {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 25, 10, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    when(healthProperty.getEksNameSpace()).thenReturn("pg-ledger");
    when(healthProperty.getCloudWatchLogGroup()).thenReturn("/non-prod/general-ledger/");
    Map<String, String> map = Collections.singletonMap("totalTimeout", "1");
    JSONArray jsonArray = new JSONArray();
    jsonArray.put(map);
    when(cloudWatchLogQuery.executeQuery(anyString(), anyLong(), anyLong(), anyString())).thenReturn(jsonArray.toString());
    String result = healthService.getTimeOutErrorWithDateRange(startTime, endTime);
    assertNotNull(result);

  }

  @Test
  public void testGetTimeOutErrorWithDateRange_Failure() {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 26, 10, 18);
    assertThrows(ValidationException.class, () -> healthService.getTimeOutErrorWithDateRange(startTime, endTime));

  }

  @Test
  public void testGetTop10ResponseByDateRange_Success() throws Exception {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 2, 13, 25);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 2, 13, 26);
    when(healthProperty.getEksNameSpace()).thenReturn("pg-ledger");
    when(healthProperty.getCloudWatchLogGroup()).thenReturn("/non-prod/general-ledger/");
    Map<String, String> map = Collections.singletonMap("minimum_response_time", "8");
    JSONArray jsonArray = new JSONArray();
    jsonArray.put(map);
    when(cloudWatchLogQuery.executeQuery(anyString(), anyLong(), anyLong(), anyString())).thenReturn(jsonArray.toString());
    String result = healthService.getTop10ResponseWithDateRange(startTime, endTime);
    assertNotNull(result);

  }

  @Test
  public void tesGetTop10ResponseByDateRange_Failure() {
    LocalDateTime startTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 26, 10, 18);
    assertThrows(ValidationException.class, () -> healthService.getTop10ResponseWithDateRange(startTime, endTime));
  }
  @Test
  public void tesGetTop10RpsWithDateRange_Success() throws Exception {

    LocalDateTime startTime = LocalDateTime.of(2024, 4, 2, 13, 25);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 2, 13, 26);
    when(healthProperty.getEksNameSpace()).thenReturn("pg-ledger");
    when(healthProperty.getCloudWatchLogGroup()).thenReturn("/non-prod/general-ledger/");
    Map<String, String> map = Collections.singletonMap("all", "2");
    JSONArray jsonArray = new JSONArray();
    jsonArray.put(map);
    when(cloudWatchLogQuery.executeQuery(anyString(), anyLong(), anyLong(), anyString())).thenReturn(jsonArray.toString());
    String result = healthService.getTop10ResponseWithDateRange(startTime, endTime);
    assertNotNull(result);

  }

  @Test
  public void tesGetTop10RpsWithDateRange_Failure() {
    LocalDateTime startTime = LocalDateTime.of(2024, 4, 28, 10, 18);
    LocalDateTime endTime = LocalDateTime.of(2024, 4, 26, 10, 18);
    assertThrows(ValidationException.class, () -> healthService.getTop10ResponseWithDateRange(startTime, endTime));
  }


}
