import api from './client/axiosInstance'
import _ from 'lodash'

export const getProfilesService = async () => {
    const response = await api().get('profile')

    const data = response?.data?.profiles || []

    return _.take(data, 250)
}

export const searchProfilesService = async (name) => {
    const response = await api().get(`profile/search?profile-name=${name}`)

    return response.data
}

export const getProfileByIdService = async (id) => {
    const response = await api().get(`profile/${id}`)

    return response.data
}

export const createProfileService = async (data) => {
    const response = await api().post('profile', data)

    return response.data
}

export const updateProfileService = async ({ id, status, reason, ...data }) => {
    const response = await api().put(`profile/${id}`, data)
    await api().patch(`profile/${id}/status`, { status, reason })

    return response.data
}