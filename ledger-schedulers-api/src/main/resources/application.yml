spring:
  main:
    allow-bean-definition-overriding: true
  datasource:
    transaction-rw:
      url: **************************************************************************
      username: postgres
      password: postgres
      platform: postgres
      maximumPoolSize: 20
      connectionTimeout: 1000
    transaction-ro:
      url: **************************************************************************
      username: postgres
      password: postgres
      platform: postgres
      maximumPoolSize: 20
      connectionTimeout: 1000
    account-ro:
      url: **********************************************************************
      username: postgres
      password: postgres
      platform: postgres
      maximumPoolSize: 20
      connectionTimeout: 1000
  jackson:
    default-property-inclusion: non_empty
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: true
  jpa:
    properties:
      hibernate:
        jdbc.lob.non_contextual_creation: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          batch_size: 100
        order_inserts: true
        order_updates: true
        generate_statistics: false
app:
  name: SchedulerAPI
scheduler:
  balance:
    snapshot: 0 10 0/6 ? * *
logging:
  level:
    com:
      zaxxer:
        hikari: INFO
      peoplestrust: ${LOG_LEVEL_COM_PEOPLESTRUST}
    root: WARN  # Change this to ERROR for even fewer logs
    org.hibernate.SQL: ${LOG_LEVEL_HIBERNATE}
    APIPayloadLogger: ${LOG_LEVEL_API_PAYLOAD_LOGGER}
    PerfLogger: ${LOG_LEVEL_PERF_LOGGER}
    FlowLogger: ${LOG_LEVEL_FLOW_LOGGER}

