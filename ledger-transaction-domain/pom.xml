<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <artifactId>ledger-parent-domain</artifactId>
    <groupId>com.peoplestrust</groupId>
    <relativePath>../ledger-parent-domain/pom.xml</relativePath>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <artifactId>ledger-transaction-domain</artifactId>
  <name>Ledger::Transaction::Domain</name>
  <build>
    <plugins>
      <!-- A Maven plugin to support the OpenAPI generator project -->
      <plugin>
        <artifactId>openapi-generator-maven-plugin</artifactId>
        <executions>
          <execution>
            <configuration>
              <addCompileSourceRoot>true</addCompileSourceRoot>
              <configOptions>
                <dateLibrary>java8</dateLibrary>
                <useJakartaEe>true</useJakartaEe>
                <interfaceOnly>true</interfaceOnly>
                <performBeanValidation>true</performBeanValidation>
                <sourceFolder>generated-sources</sourceFolder>
                <useBeanValidation>true</useBeanValidation>
              </configOptions>
              <generateApiDocumentation>true</generateApiDocumentation>
              <generateApiTests>false</generateApiTests>
              <generateApis>false</generateApis>
              <generateModelDocumentation>false</generateModelDocumentation>
              <generateModelTests>false</generateModelTests>
              <generateModels>true</generateModels>
              <generateSupportingFiles>false</generateSupportingFiles>
              <generatorName>java</generatorName>
              <inputSpec>${project.basedir}/../common-domain/src/main/schema/transaction-system.yaml</inputSpec>
              <library>resttemplate</library>
              <modelPackage>com.peoplestrust.transaction.domain.model</modelPackage>
              <output>${project.build.directory}</output>
              <skipValidateSpec>true</skipValidateSpec>
            </configuration>
            <goals>
              <goal>generate</goal>
            </goals>
            <id>transaction-system</id>
          </execution>
          <execution>
            <configuration>
              <addCompileSourceRoot>true</addCompileSourceRoot>
              <configOptions>
                <dateLibrary>java8</dateLibrary>
                <useJakartaEe>true</useJakartaEe>
                <interfaceOnly>true</interfaceOnly>
                <performBeanValidation>true</performBeanValidation>
                <sourceFolder>generated-sources</sourceFolder>
                <useBeanValidation>true</useBeanValidation>
              </configOptions>
              <generateApiDocumentation>true</generateApiDocumentation>
              <generateApiTests>false</generateApiTests>
              <generateApis>false</generateApis>
              <generateModelDocumentation>false</generateModelDocumentation>
              <generateModelTests>false</generateModelTests>
              <generateModels>true</generateModels>
              <generateSupportingFiles>false</generateSupportingFiles>
              <generatorName>java</generatorName>
              <inputSpec>${project.basedir}/../common-domain/src/main/schema/transaction-adminui.yaml</inputSpec>
              <library>resttemplate</library>
              <modelPackage>com.peoplestrust.transaction.domain.model</modelPackage>
              <output>${project.build.directory}</output>
              <skipValidateSpec>true</skipValidateSpec>
            </configuration>
            <goals>
              <goal>generate</goal>
            </goals>
            <id>transaction-adminui</id>
          </execution>
          <execution>
            <configuration>
              <addCompileSourceRoot>true</addCompileSourceRoot>
              <configOptions>
                <dateLibrary>java8</dateLibrary>
                <useJakartaEe>true</useJakartaEe>
                <interfaceOnly>true</interfaceOnly>
                <performBeanValidation>true</performBeanValidation>
                <sourceFolder>generated-sources</sourceFolder>
                <useBeanValidation>true</useBeanValidation>
              </configOptions>
              <generateApiDocumentation>true</generateApiDocumentation>
              <generateApiTests>false</generateApiTests>
              <generateApis>false</generateApis>
              <generateModelDocumentation>false</generateModelDocumentation>
              <generateModelTests>false</generateModelTests>
              <generateModels>true</generateModels>
              <generateSupportingFiles>false</generateSupportingFiles>
              <generatorName>java</generatorName>
              <inputSpec>${project.basedir}/../common-domain/src/main/schema/transaction-internal.yaml</inputSpec>
              <library>resttemplate</library>
              <modelPackage>com.peoplestrust.transaction.domain.model</modelPackage>
              <output>${project.build.directory}</output>
              <skipValidateSpec>true</skipValidateSpec>
            </configuration>
            <goals>
              <goal>generate</goal>
            </goals>
            <id>transaction-internal</id>
          </execution>
        </executions>
        <groupId>org.openapitools</groupId>
        <version>${openapi.generator.version}</version>
      </plugin>
    </plugins>
  </build>
</project>