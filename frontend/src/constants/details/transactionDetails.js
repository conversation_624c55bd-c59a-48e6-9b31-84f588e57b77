import generateDate from "utils/generateDate"
import amountFormat from "utils/amountFormat"

const details = [
    {
        title: 'Payment Category',
        key: 'payment_category'
    },
    {
        title: 'Transaction Flow',
        key: 'transaction_flow'
    },
    {
        title: 'Amount',
        key: (item) => amountFormat(item?.amount)
    },
    {
        title: 'Monetary Unit',
        key: 'monetary_unit'
    },
    {
        title: 'Transaction Status',
        key: 'status'
    },
    {
        title: 'Acceptance Date',
        key: (item) => generateDate(item?.acceptance_date_time) || 'N/A'
    },
    {
        title: 'Due Date',
        key: (item) => generateDate(item?.due_date_time) || 'N/A'
    },

]

export default details