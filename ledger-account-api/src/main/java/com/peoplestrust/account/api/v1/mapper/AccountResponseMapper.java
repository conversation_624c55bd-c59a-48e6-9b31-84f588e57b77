package com.peoplestrust.account.api.v1.mapper;

import com.peoplestrust.account.api.v1.model.Account;
import com.peoplestrust.account.api.v1.model.Balance;
import com.peoplestrust.account.domain.model.CreateLedgerAccountResponse;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountBalanceHistoryResponse;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountResponse;
import com.peoplestrust.account.domain.model.RetrieveLedgerAccountsResponseAccountsInner;
import com.peoplestrust.account.domain.model.UpdateLedgerAccountResponse;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;

import com.peoplestrust.transaction.domain.model.BalanceSnapshotResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface AccountResponseMapper {

    /**
     * Account -- DTO to domain POST response mapper
     *
     * @param account
     * @return
     */
    @Mappings({
            @Mapping(source = "refId", target = "refId", qualifiedByName = "stringToUUID"),
            @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet")
    })
    CreateLedgerAccountResponse fromAccountToAccountPostResponse(Account account);

    /**
     * Account -- DTO to domain PUT response mapper
     *
     * @param account
     * @return
     */
    @Mappings({
            @Mapping(source = "refId", target = "refId", qualifiedByName = "stringToUUID"),
            @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet"),
            @Mapping(source = "updatedDateTime", target = "updatedDateTime", qualifiedByName = "dateTimeToOffSet")
    })
    UpdateLedgerAccountResponse fromAccountToAccountPutResponse(Account account);

    /**
     * Account -- DTO to domain response mapper
     *
     * @param account
     * @return
     */
    @Mappings({
            @Mapping(source = "refId", target = " refId", qualifiedByName = "stringToUUID")
    })
    RetrieveLedgerAccountsResponseAccountsInner fromAccountToRetrieveLedgerAccountsResponse(
            Account account);

    List<RetrieveLedgerAccountsResponseAccountsInner> fromAccountsToRetrieveLedgerAccountsResponse(
            List<Account> accounts);

    /**
     * Balance -- DTO to Balance domain response mapper.
     *
     * @param balance balance DTO
     * @return
     */
    @Mappings({
            @Mapping(source = "totalAmountCredit", target = "totalCreditAmount"),
            @Mapping(source = "totalAmountDebit", target = "totalDebitAmount"),
            @Mapping(source = "totalReserveAmount", target = "totalReserveAmount"),
            @Mapping(source = "totalAmount", target = "totalAmount"),
            @Mapping(source = "effectiveToDateTime", target = "effectiveOn")
    })
    RetrieveLedgerAccountBalanceHistoryResponse fromBalanceToRetrieveLedgerAccountBalanceHistoryResponse(
            Balance balance);

    List<RetrieveLedgerAccountBalanceHistoryResponse> fromBalanceSnapshotResponseListToRetrieveLedgerAccountBalanceHistoryResponseList(
            List<BalanceSnapshotResponse> balanceList);

    @Mappings({
            @Mapping(source = "totalAmountCredit", target = "totalCreditAmount"),
            @Mapping(source = "totalAmountDebit", target = "totalDebitAmount"),
            @Mapping(source = "totalReserveAmount", target = "totalReserveAmount"),
            @Mapping(source = "totalAmount", target = "totalAmount"),
            @Mapping(source = "effectiveToDateTime", target = "effectiveOn")
    })
    RetrieveLedgerAccountBalanceHistoryResponse fromBalanceSnapshotResponseToRetrieveLedgerAccountBalanceHistoryResponse(BalanceSnapshotResponse balanceSnapshotResponse);

    /**
     * Account -- DTO to domain response mapper
     *
     * @param account
     * @return
     */
    @Mappings({
            @Mapping(source = "refId", target = "refId", qualifiedByName = "stringToUUID"),
            @Mapping(source = "createdDateTime", target = "createdDateTime", qualifiedByName = "dateTimeToOffSet"),
            @Mapping(source = "updatedDateTime", target = "updatedDateTime", qualifiedByName = "dateTimeToOffSet")
    })
    RetrieveLedgerAccountResponse fromAccountToAccountGetResponse(Account account);

    /**
     * Utility function to map string to UUID
     *
     * @param uuid
     * @return
     */
    @Named(value = "stringToUUID")
    default UUID stringToUUID(String uuid) {
        return UUID.fromString(uuid);
    }

    /**
     * Utility function
     *
     * @param dt
     * @return
     */
    @Named(value = "dateTimeToOffSet")
    default OffsetDateTime dateTimeToOffSet(LocalDateTime dt) {
        if (dt != null) {
            return OffsetDateTime.of(dt, ZoneOffset.UTC);
        } else {
            return null;
        }
    }
}
